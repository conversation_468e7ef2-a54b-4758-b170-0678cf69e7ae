{"data_mtime": 1750703628, "dep_lines": [13, 15, 16, 17, 5, 7, 8, 9, 10, 11, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["packaging._parser", "packaging._tokenizer", "packaging.specifiers", "packaging.utils", "__future__", "operator", "os", "platform", "sys", "typing", "builtins", "_frozen_importlib", "_operator", "_typeshed", "abc", "typing_extensions"], "hash": "0d069835202bfa5d84c11c73f98fc88417a57f8e", "id": "packaging.markers", "ignore_all": true, "interface_hash": "5b1ab95de3619879af26b7fe53bf0ddebcf3a02c", "mtime": 1747857425, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/packaging/markers.py", "plugin_data": null, "size": 12049, "suppressed": [], "version_id": "1.16.1"}