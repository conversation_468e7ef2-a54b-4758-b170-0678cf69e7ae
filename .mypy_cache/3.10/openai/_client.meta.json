{"data_mtime": 1750703639, "dep_lines": [60, 61, 63, 65, 67, 68, 69, 70, 71, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 38, 11, 12, 13, 22, 27, 28, 29, 31, 38, 3, 5, 6, 7, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 25, 25, 25, 25, 25, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 10, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.resources.beta.beta", "openai.resources.chat.chat", "openai.resources.audio.audio", "openai.resources.evals.evals", "openai.resources.uploads.uploads", "openai.resources.responses.responses", "openai.resources.containers.containers", "openai.resources.fine_tuning.fine_tuning", "openai.resources.vector_stores.vector_stores", "openai.resources.beta", "openai.resources.chat", "openai.resources.audio", "openai.resources.evals", "openai.resources.files", "openai.resources.images", "openai.resources.models", "openai.resources.batches", "openai.resources.uploads", "openai.resources.responses", "openai.resources.containers", "openai.resources.embeddings", "openai.resources.completions", "openai.resources.fine_tuning", "openai.resources.moderations", "openai.resources.vector_stores", "openai._exceptions", "openai._qs", "openai._types", "openai._utils", "openai._compat", "openai._version", "openai._streaming", "openai._base_client", "openai.resources", "__future__", "os", "typing", "typing_extensions", "httpx", "openai", "builtins", "_frozen_importlib", "abc", "httpx._client", "httpx._config", "httpx._models", "httpx._urls", "openai._constants", "openai._resource"], "hash": "bedfabef76e872676628af50c3cd936066ff15d5", "id": "openai._client", "ignore_all": true, "interface_hash": "2269c79f846e795e43d106880d58a39d5b151724", "mtime": 1747857566, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/_client.py", "plugin_data": null, "size": 38062, "suppressed": [], "version_id": "1.16.1"}