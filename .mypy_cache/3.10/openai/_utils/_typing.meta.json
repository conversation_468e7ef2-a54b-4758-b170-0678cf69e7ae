{"data_mtime": 1750703638, "dep_lines": [16, 7, 17, 18, 1, 3, 4, 5, 7, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 10, 5, 5, 20, 5, 30, 30, 30], "dependencies": ["openai._utils._utils", "collections.abc", "openai._types", "openai._compat", "__future__", "sys", "typing", "typing_extensions", "collections", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "094ea3ee97e566219b884b50f51445707bfe7a83", "id": "openai._utils._typing", "ignore_all": true, "interface_hash": "53a64752d899960080145c9c8c1f558b2b3578f7", "mtime": 1747857566, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/_utils/_typing.py", "plugin_data": null, "size": 4602, "suppressed": [], "version_id": "1.16.1"}