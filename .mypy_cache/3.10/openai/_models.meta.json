{"data_mtime": 1750703639, "dep_lines": [23, 25, 36, 52, 65, 68, 1, 3, 4, 5, 6, 7, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 25, 5, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic.fields", "openai._types", "openai._utils", "openai._compat", "openai._constants", "pydantic_core.core_schema", "__future__", "os", "inspect", "typing", "datetime", "typing_extensions", "pydantic", "builtins", "_frozen_importlib", "abc", "httpx", "httpx._config", "openai._utils._utils", "pydantic._internal", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.config", "pydantic.deprecated", "pydantic.deprecated.config", "pydantic.main", "pydantic.type_adapter", "pydantic_core", "re"], "hash": "900e412aac473d89525f886eb3f9229232e9d3f0", "id": "openai._models", "ignore_all": true, "interface_hash": "cc5917817db7a90de0a9a17f1cf91317f3205577", "mtime": 1747857566, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/_models.py", "plugin_data": null, "size": 30811, "suppressed": [], "version_id": "1.16.1"}