{"data_mtime": 1750703639, "dep_lines": [36, 85, 88, 89, 9, 10, 11, 12, 13, 14, 15, 16, 17, 35, 37, 85, 86, 355, 3, 5, 6, 7, 113, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["openai._utils._logs", "openai.lib.azure", "openai.lib._old_api", "openai.lib.streaming", "openai.types", "openai._types", "openai._utils", "openai._client", "openai._models", "openai._version", "openai._response", "openai._constants", "openai._exceptions", "openai._base_client", "openai._legacy_response", "openai.lib", "openai.version", "openai._module_client", "__future__", "os", "typing", "typing_extensions", "httpx", "builtins", "_frozen_importlib", "abc", "httpx._client", "httpx._config", "httpx._urls", "types"], "hash": "99172d9c6f03bac53490e380e3e021ff76e8b9bb", "id": "openai", "ignore_all": true, "interface_hash": "f04559053f0212bf08b2e6f71b59eb05d83fc08c", "mtime": 1747857566, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/__init__.py", "plugin_data": null, "size": 10544, "suppressed": [], "version_id": "1.16.1"}