{"data_mtime": 1750703639, "dep_lines": [14, 10, 11, 3, 5, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.chat", "openai._utils", "openai._models", "__future__", "typing", "typing_extensions", "httpx", "builtins", "_frozen_importlib", "abc", "httpx._models", "openai._utils._utils", "openai.types", "openai.types.chat.chat_completion", "openai.types.completion_usage", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "2bb883cc43bab3d15d8f113e7a96b171979bd52f", "id": "openai._exceptions", "ignore_all": true, "interface_hash": "da81b5575738936181f101a1e4317cdf0597dba8", "mtime": 1747857566, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/_exceptions.py", "plugin_data": null, "size": 4798, "suppressed": [], "version_id": "1.16.1"}