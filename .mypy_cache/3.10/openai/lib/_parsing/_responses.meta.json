{"data_mtime": 1750703639, "dep_lines": [15, 29, 9, 14, 16, 10, 11, 12, 13, 1, 3, 4, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.lib._parsing._completions", "openai.types.chat.completion_create_params", "openai.lib._tools", "openai.lib._pydantic", "openai.types.responses", "openai._types", "openai._utils", "openai._compat", "openai._models", "__future__", "json", "typing", "typing_extensions", "pydantic", "builtins", "_frozen_importlib", "abc", "openai.types", "openai.types.responses.computer_tool_param", "openai.types.responses.file_search_tool_param", "openai.types.responses.function_tool_param", "openai.types.responses.parsed_response", "openai.types.responses.response", "openai.types.responses.response_format_text_json_schema_config_param", "openai.types.responses.response_function_tool_call", "openai.types.responses.tool_param", "openai.types.responses.web_search_tool_param", "openai.types.shared_params", "openai.types.shared_params.comparison_filter", "openai.types.shared_params.compound_filter", "openai.types.shared_params.response_format_json_object", "openai.types.shared_params.response_format_text", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "6b68427059b0f5bf8346dfe00668fecd91e4891b", "id": "openai.lib._parsing._responses", "ignore_all": true, "interface_hash": "c6ee97c33e872b601525e0fd51ce74109244964e", "mtime": 1747857566, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/lib/_parsing/_responses.py", "plugin_data": null, "size": 5980, "suppressed": [], "version_id": "1.16.1"}