{"data_mtime": 1750703639, "dep_lines": [10, 11, 12, 13, 14, 15, 16, 17, 1, 3, 4, 5, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai._types", "openai._utils", "openai._client", "openai._compat", "openai._models", "openai._streaming", "openai._exceptions", "openai._base_client", "__future__", "os", "inspect", "typing", "typing_extensions", "httpx", "builtins", "_frozen_importlib", "abc", "httpx._client", "httpx._config", "httpx._models", "httpx._urls", "openai._constants", "openai._utils._utils", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "types"], "hash": "ead6b986bd63b6bfc7d32968a7a259a905f6dfe8", "id": "openai.lib.azure", "ignore_all": true, "interface_hash": "5e0b84488a3373020029d684e53ccf53f16e7cfc", "mtime": 1747857566, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/lib/azure.py", "plugin_data": null, "size": 25621, "suppressed": [], "version_id": "1.16.1"}