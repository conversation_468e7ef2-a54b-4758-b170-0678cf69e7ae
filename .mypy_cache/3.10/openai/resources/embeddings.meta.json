{"data_mtime": 1750703639, "dep_lines": [13, 21, 22, 12, 13, 14, 15, 16, 17, 18, 19, 20, 3, 5, 6, 7, 8, 10, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 20, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.embedding_create_params", "openai.types.embedding_model", "openai.types.create_embedding_response", "openai._legacy_response", "openai.types", "openai._types", "openai._utils", "openai._compat", "openai._extras", "openai._resource", "openai._response", "openai._base_client", "__future__", "array", "base64", "typing", "typing_extensions", "httpx", "openai", "builtins", "_frozen_importlib", "abc", "httpx._config", "openai._models", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "4345692c10d4d0f6974a6d40d30690c0373eeac6", "id": "openai.resources.embeddings", "ignore_all": true, "interface_hash": "d890c484aa5c78617c92fedba151db9f0aad49e9", "mtime": 1747857566, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/resources/embeddings.py", "plugin_data": null, "size": 12200, "suppressed": [], "version_id": "1.16.1"}