{"data_mtime": 1750703639, "dep_lines": [15, 32, 11, 11, 11, 27, 28, 29, 30, 31, 10, 11, 12, 13, 14, 23, 24, 25, 26, 3, 5, 6, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 10, 20, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.resources.evals.runs.runs", "openai.types.shared_params.metadata", "openai.types.eval_list_params", "openai.types.eval_create_params", "openai.types.eval_update_params", "openai.types.eval_list_response", "openai.types.eval_create_response", "openai.types.eval_delete_response", "openai.types.eval_update_response", "openai.types.eval_retrieve_response", "openai._legacy_response", "openai.types", "openai._types", "openai._utils", "openai._compat", "openai._resource", "openai._response", "openai.pagination", "openai._base_client", "__future__", "typing", "typing_extensions", "httpx", "openai", "builtins", "_frozen_importlib", "abc", "httpx._config", "openai._models", "openai.resources.evals.runs", "openai.types.graders", "openai.types.graders.score_model_grader_param", "openai.types.graders.string_check_grader_param", "openai.types.responses", "openai.types.responses.response_input_text_param", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "91b72eb4343ea48206188f31485a51218037e8f7", "id": "openai.resources.evals.evals", "ignore_all": true, "interface_hash": "0e6df8d1c3edad9f72427f400d1e1644001a25c9", "mtime": 1747857566, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/resources/evals/evals.py", "plugin_data": null, "size": 26036, "suppressed": [], "version_id": "1.16.1"}