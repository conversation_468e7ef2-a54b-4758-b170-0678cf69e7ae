{"data_mtime": 1750703639, "dep_lines": [18, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 18, 20, 26, 28, 11, 12, 13, 14, 15, 16, 17, 19, 3, 5, 6, 7, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.chat.completion_create_params", "openai.lib.streaming.chat", "openai.types.chat.chat_completion", "openai.types.chat.chat_completion_chunk", "openai.types.chat.parsed_chat_completion", "openai.types.chat.chat_completion_tool_param", "openai.types.chat.chat_completion_audio_param", "openai.types.chat.chat_completion_message_param", "openai.types.chat.chat_completion_stream_options_param", "openai.types.chat.chat_completion_prediction_content_param", "openai.types.chat.chat_completion_tool_choice_option_param", "openai.types.chat", "openai.lib._parsing", "openai.types.chat_model", "openai.types.shared_params", "openai._legacy_response", "openai._types", "openai._utils", "openai._compat", "openai._resource", "openai._response", "openai._streaming", "openai._base_client", "__future__", "typing", "functools", "typing_extensions", "httpx", "openai", "builtins", "_frozen_importlib", "abc", "httpx._config", "openai._models", "openai.lib", "openai.lib.streaming", "openai.lib.streaming.chat._completions", "openai.types", "openai.types.chat.chat_completion_assistant_message_param", "openai.types.chat.chat_completion_content_part_image_param", "openai.types.chat.chat_completion_content_part_input_audio_param", "openai.types.chat.chat_completion_content_part_param", "openai.types.chat.chat_completion_content_part_refusal_param", "openai.types.chat.chat_completion_content_part_text_param", "openai.types.chat.chat_completion_developer_message_param", "openai.types.chat.chat_completion_function_call_option_param", "openai.types.chat.chat_completion_function_message_param", "openai.types.chat.chat_completion_message_tool_call_param", "openai.types.chat.chat_completion_named_tool_choice_param", "openai.types.chat.chat_completion_system_message_param", "openai.types.chat.chat_completion_tool_message_param", "openai.types.chat.chat_completion_user_message_param", "openai.types.shared_params.function_definition", "openai.types.shared_params.response_format_json_object", "openai.types.shared_params.response_format_json_schema", "openai.types.shared_params.response_format_text", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "fac9b20d06df4480f3a1b15ea4804a86667abe1d", "id": "openai.resources.beta.chat.completions", "ignore_all": true, "interface_hash": "0e8f6a681e5ff5c263c57171c4c50c9ba409d98b", "mtime": 1747857566, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/resources/beta/chat/completions.py", "plugin_data": null, "size": 29122, "suppressed": [], "version_id": "1.16.1"}