{"data_mtime": 1750703639, "dep_lines": [6, 16, 24, 7, 5, 15, 3, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.resources.beta.chat.chat", "openai.resources.beta.threads.threads", "openai.resources.beta.realtime.realtime", "openai.resources.beta.assistants", "openai._compat", "openai._resource", "__future__", "builtins", "_frozen_importlib", "abc", "openai.resources.beta.chat", "openai.resources.beta.realtime", "openai.resources.beta.threads", "typing"], "hash": "58b9779af9398d8844e04664af37305cebb66c32", "id": "openai.resources.beta.beta", "ignore_all": true, "interface_hash": "d334cd56920f3acb74eeab364480d61f6bd8560c", "mtime": 1747857566, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/resources/beta/beta.py", "plugin_data": null, "size": 5478, "suppressed": [], "version_id": "1.16.1"}