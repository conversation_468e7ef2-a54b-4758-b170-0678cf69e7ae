{"data_mtime": 1750703639, "dep_lines": [22, 12, 49, 34, 34, 34, 48, 50, 51, 52, 53, 54, 55, 56, 34, 40, 11, 20, 21, 30, 31, 32, 33, 39, 3, 5, 6, 7, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.resources.beta.threads.runs.runs", "openai.resources.beta.threads.messages", "openai.types.beta.threads.run", "openai.types.beta.thread_create_params", "openai.types.beta.thread_update_params", "openai.types.beta.thread_create_and_run_params", "openai.types.beta.thread", "openai.types.shared.chat_model", "openai.types.beta.thread_deleted", "openai.types.shared_params.metadata", "openai.types.beta.assistant_tool_param", "openai.types.beta.assistant_stream_event", "openai.types.beta.assistant_tool_choice_option_param", "openai.types.beta.assistant_response_format_option_param", "openai.types.beta", "openai.lib.streaming", "openai._legacy_response", "openai._types", "openai._utils", "openai._compat", "openai._resource", "openai._response", "openai._streaming", "openai._base_client", "__future__", "typing", "functools", "typing_extensions", "httpx", "openai", "builtins", "_frozen_importlib", "abc", "httpx._config", "openai._models", "openai._utils._utils", "openai.lib", "openai.lib.streaming._assistants", "openai.resources.beta.threads.runs", "openai.types", "openai.types.beta.assistant_tool_choice_function_param", "openai.types.beta.assistant_tool_choice_param", "openai.types.beta.code_interpreter_tool_param", "openai.types.beta.file_search_tool_param", "openai.types.beta.function_tool_param", "openai.types.beta.threads", "openai.types.beta.threads.image_file_content_block_param", "openai.types.beta.threads.image_file_param", "openai.types.beta.threads.image_url_content_block_param", "openai.types.beta.threads.image_url_param", "openai.types.beta.threads.text_content_block_param", "openai.types.shared_params", "openai.types.shared_params.function_definition", "openai.types.shared_params.response_format_json_object", "openai.types.shared_params.response_format_json_schema", "openai.types.shared_params.response_format_text", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "types"], "hash": "d8ce12a140dfc7286e38e9ceca163e481144c00e", "id": "openai.resources.beta.threads.threads", "ignore_all": true, "interface_hash": "e8ddc1fb1409cce4d7bd5ad73f6542dbb42a0201", "mtime": 1747857566, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/openai/resources/beta/threads/threads.py", "plugin_data": null, "size": 95669, "suppressed": [], "version_id": "1.16.1"}