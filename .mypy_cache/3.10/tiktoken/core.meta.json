{"data_mtime": 1750703632, "dep_lines": [4, 13, 404, 1, 3, 5, 9, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 7], "dep_prios": [5, 25, 20, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["concurrent.futures", "numpy.typing", "tiktoken.registry", "__future__", "functools", "typing", "tiktoken", "numpy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "numpy._typing", "numpy._typing._nbit_base", "types", "typing_extensions"], "hash": "1bd4575191980766693d2df79cc8266ae82a0002", "id": "tiktoken.core", "ignore_all": true, "interface_hash": "a5b950736164f8787dece56a74c2300929beab81", "mtime": 1746710739, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/tiktoken/core.py", "plugin_data": null, "size": 17278, "suppressed": ["regex"], "version_id": "1.16.1"}