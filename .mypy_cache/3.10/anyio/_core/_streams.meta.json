{"data_mtime": 1750703628, "dep_lines": [7, 1, 3, 4, 5, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["anyio.streams.memory", "__future__", "math", "typing", "warnings", "builtins", "_frozen_importlib", "abc", "anyio._core._typedattr", "anyio.abc", "anyio.abc._resources", "anyio.abc._streams", "anyio.streams"], "hash": "e2f202537d7cd07e789c3bb4305624dd0a014fb7", "id": "anyio._core._streams", "ignore_all": true, "interface_hash": "b1c701c3c502af574baf6879e34dca665cbf3c70", "mtime": 1747857487, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/anyio/_core/_streams.py", "plugin_data": null, "size": 1804, "suppressed": [], "version_id": "1.16.1"}