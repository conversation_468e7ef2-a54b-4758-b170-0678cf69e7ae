{"data_mtime": 1750703627, "dep_lines": [6, 7, 10, 1, 3, 4, 6, 12, 1, 1, 1], "dep_prios": [10, 5, 25, 5, 5, 5, 20, 25, 5, 30, 30], "dependencies": ["trio._core", "trio.abc", "collections.abc", "__future__", "functools", "typing", "trio", "typing_extensions", "builtins", "_frozen_importlib", "abc"], "hash": "68ef4bfb6c6a1421648c2bc0d4a05f3dfea65a31", "id": "trio.testing._trio_test", "ignore_all": true, "interface_hash": "45194755575de728e683627969e6a4240ffa052d", "mtime": 1749669151, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/trio/testing/_trio_test.py", "plugin_data": null, "size": 1386, "suppressed": [], "version_id": "1.16.1"}