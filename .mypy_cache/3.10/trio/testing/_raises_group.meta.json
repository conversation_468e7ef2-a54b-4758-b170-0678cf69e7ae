{"data_mtime": 1750703627, "dep_lines": [26, 16, 24, 1, 3, 4, 5, 7, 8, 19, 23, 27, 1, 1, 1, 1, 1, 1, 49], "dep_prios": [25, 5, 25, 5, 5, 10, 5, 5, 5, 25, 25, 25, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["_pytest._code.code", "trio._util", "collections.abc", "__future__", "re", "sys", "abc", "textwrap", "typing", "builtins", "types", "typing_extensions", "_collections_abc", "_frozen_importlib", "_pytest", "_pytest._code", "_typeshed", "enum"], "hash": "e0971e1344ad357f4f359d3b91364c302904ab9b", "id": "trio.testing._raises_group", "ignore_all": true, "interface_hash": "017b4f6e940823ae029201c4796fe4bd31516dcd", "mtime": 1749669151, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/trio/testing/_raises_group.py", "plugin_data": null, "size": 40807, "suppressed": ["exceptiongroup"], "version_id": "1.16.1"}