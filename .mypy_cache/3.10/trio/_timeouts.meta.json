{"data_mtime": 1750703627, "dep_lines": [11, 1, 3, 4, 5, 6, 8, 194, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 10, 10, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "__future__", "math", "sys", "contextlib", "typing", "trio", "inspect", "builtins", "_frozen_importlib", "abc", "trio._core", "trio._core._run", "types"], "hash": "6f79076d8670bfe08b5d1cddb02f8aa262975d83", "id": "trio._timeouts", "ignore_all": true, "interface_hash": "849e34bb47a12b99915d2e4954f470e992a8203c", "mtime": 1749669151, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/trio/_timeouts.py", "plugin_data": null, "size": 6230, "suppressed": [], "version_id": "1.16.1"}