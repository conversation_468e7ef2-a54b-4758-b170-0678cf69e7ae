{"data_mtime": 1750703627, "dep_lines": [16, 1, 10, 13, 14, 16, 36, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 20, 5, 5, 30, 30, 30, 30], "dependencies": ["trio._socket", "__future__", "socket", "sys", "typing", "trio", "contextlib", "builtins", "_frozen_importlib", "_typeshed", "abc", "types"], "hash": "179799e019ab4a9c3f8e5a3ff1fdeb1754416440", "id": "trio.socket", "ignore_all": true, "interface_hash": "25eeb046b99a4c7b250ad076653534e020338510", "mtime": 1749669151, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/trio/socket.py", "plugin_data": null, "size": 22916, "suppressed": [], "version_id": "1.16.1"}