{"data_mtime": 1750703627, "dep_lines": [25, 10, 19, 1, 3, 4, 6, 8, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 5, 10, 5, 10, 10, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["trio._core._parking_lot", "trio._core", "trio._util", "__future__", "math", "typing", "attrs", "trio", "types", "builtins", "_frozen_importlib", "_typeshed", "abc", "attr", "attr.setters", "outcome", "outcome._impl", "trio._core._exceptions", "trio._core._generated_run", "trio._core._ki", "trio._core._run", "trio.lowlevel", "typing_extensions"], "hash": "e7d0219fab00f4d78a6ac7b317754baab4e07221", "id": "trio._sync", "ignore_all": true, "interface_hash": "3c74945b045742abd45a49a44b97eaa389025c81", "mtime": 1749669151, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/trio/_sync.py", "plugin_data": null, "size": 31392, "suppressed": [], "version_id": "1.16.1"}