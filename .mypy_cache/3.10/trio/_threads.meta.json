{"data_mtime": 1750703627, "dep_lines": [34, 18, 26, 27, 30, 1, 3, 4, 5, 6, 7, 8, 9, 11, 12, 14, 16, 32, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 5, 25, 5, 10, 10, 10, 10, 10, 5, 5, 5, 10, 5, 10, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["trio._core._traps", "trio._core", "trio._sync", "trio._util", "collections.abc", "__future__", "contextlib", "<PERSON><PERSON><PERSON>", "inspect", "queue", "threading", "itertools", "typing", "attrs", "outcome", "sniffio", "trio", "typing_extensions", "builtins", "_contextvars", "_frozen_importlib", "_queue", "_thread", "abc", "attr", "attr.setters", "outcome._impl", "trio._core._entry_queue", "trio._core._ki", "trio._core._local", "trio._core._run", "trio.lowlevel"], "hash": "ba9a8a3d12528244fcde56b8681d163b184dbfd1", "id": "trio._threads", "ignore_all": true, "interface_hash": "4c476bed189d2a45a54dd237c70eab98b3c8f56c", "mtime": 1749669151, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/trio/_threads.py", "plugin_data": null, "size": 24083, "suppressed": [], "version_id": "1.16.1"}