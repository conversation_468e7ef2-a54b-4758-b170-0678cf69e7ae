{"data_mtime": 1750703627, "dep_lines": [4, 2, 4, 5, 6, 7, 9, 10, 19, 21, 29, 30, 32, 1, 1, 1, 1, 1, 35], "dep_prios": [5, 5, 20, 10, 10, 5, 5, 5, 5, 10, 25, 25, 25, 5, 30, 30, 30, 30, 25], "dependencies": ["collections.abc", "__future__", "collections", "inspect", "signal", "abc", "functools", "typing", "sniffio", "trio", "sys", "types", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "trio._core", "trio._core._exceptions"], "hash": "d848ec9fbac66ff572cf3b8132ba9666bd09767c", "id": "trio._util", "ignore_all": true, "interface_hash": "eede85b127cfd652e8dc1d02627f6afce02aeaee", "mtime": 1749669151, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/trio/_util.py", "plugin_data": null, "size": 14969, "suppressed": ["exceptiongroup"], "version_id": "1.16.1"}