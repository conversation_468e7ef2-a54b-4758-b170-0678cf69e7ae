{"data_mtime": 1750703632, "dep_lines": [5, 7, 8, 1, 2, 3, 4, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["tenacity.retry", "flow_api.configuracoes", "flow_api.token", "json", "abc", "logging", "requests", "tenacity", "builtins", "_frozen_importlib", "datetime", "requests.exceptions", "requests.models", "tenacity.asyncio", "tenacity.asyncio.retry", "tenacity.stop", "tenacity.wait", "typing"], "hash": "afa9cdb5006ebf2ed0b1bf6601b4630510ffebfe", "id": "flow_api.clients.ai_client", "ignore_all": false, "interface_hash": "cb5a3539a407d097a18a1346179c42f46596cd4c", "mtime": 1750689856, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/var/www/ciandt/pyflow_rag/flow_api/clients/ai_client.py", "plugin_data": null, "size": 3667, "suppressed": [], "version_id": "1.16.1"}