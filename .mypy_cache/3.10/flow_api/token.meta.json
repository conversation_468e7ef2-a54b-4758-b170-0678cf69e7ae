{"data_mtime": 1750703629, "dep_lines": [8, 10, 1, 2, 3, 4, 5, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 10, 10, 10, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["tenacity.retry", "flow_api.configuracoes", "requests", "json", "threading", "time", "datetime", "logging", "tenacity", "builtins", "_frozen_importlib", "_thread", "_typeshed", "abc", "http", "http.cookiejar", "json.decoder", "json.encoder", "requests.api", "requests.auth", "requests.exceptions", "requests.models", "tenacity.asyncio", "tenacity.asyncio.retry", "tenacity.stop", "tenacity.wait", "typing", "typing_extensions"], "hash": "627f99b24fd6b1a54a7ade4580e5c2169d864c2e", "id": "flow_api.token", "ignore_all": false, "interface_hash": "4377af6ef5ce2524071675930cfc652df43603dd", "mtime": 1750690977, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/var/www/ciandt/pyflow_rag/flow_api/token.py", "plugin_data": null, "size": 4311, "suppressed": [], "version_id": "1.16.1"}