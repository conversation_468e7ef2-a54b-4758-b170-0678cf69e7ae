{"data_mtime": 1750703632, "dep_lines": [29, 33, 34, 53, 75, 85, 86, 87, 88, 89, 95, 20, 22, 27, 28, 29, 30, 31, 32, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 55, 85, 94, 96, 643, 1737, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 90, 91], "dep_prios": [10, 10, 10, 5, 5, 10, 10, 10, 10, 10, 25, 5, 10, 10, 10, 20, 10, 10, 10, 10, 10, 10, 10, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 20, 25, 25, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["email.parser", "importlib.abc", "importlib.machinery", "collections.abc", "os.path", "packaging.markers", "packaging.requirements", "packaging.specifiers", "packaging.utils", "packaging.version", "_typeshed.importlib", "__future__", "sys", "_imp", "collections", "email", "errno", "functools", "importlib", "inspect", "io", "ntpath", "operator", "os", "pkgu<PERSON>", "platform", "plistlib", "posixpath", "re", "stat", "tempfile", "textwrap", "time", "types", "warnings", "zipfile", "zipimport", "typing", "packaging", "_typeshed", "typing_extensions", "__main__", "linecache", "builtins", "_frozen_importlib", "_frozen_importlib_external", "_io", "_warnings", "abc", "email._policybase", "email.message", "enum", "importlib._abc"], "hash": "8a10e905b85ccf225ec33f7d464dcc82f60bbb83", "id": "pkg_resources", "ignore_all": true, "interface_hash": "6b3ae8115f3bed8e620e5cbbd463652e4b3418fa", "mtime": 1747857421, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/pkg_resources/__init__.py", "plugin_data": null, "size": 126075, "suppressed": ["jaraco.text", "platformdirs"], "version_id": "1.16.1"}