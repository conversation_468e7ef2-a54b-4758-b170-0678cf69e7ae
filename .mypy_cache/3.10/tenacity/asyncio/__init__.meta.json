{"data_mtime": 1750703628, "dep_lines": [34, 31, 39, 42, 43, 18, 19, 20, 22, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 25, 25, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["tenacity.asyncio.retry", "tenacity._utils", "tenacity.retry", "tenacity.stop", "tenacity.wait", "functools", "sys", "typing", "tenacity", "builtins", "_frozen_importlib", "abc", "concurrent", "concurrent.futures", "concurrent.futures._base", "tenacity.after", "tenacity.before"], "hash": "8e8c9709594711b4f57227e0f23e4f04a5a587c8", "id": "tenacity.asyncio", "ignore_all": true, "interface_hash": "8e1ddfce7b0902048e9f490dbf27545bfb33a7b3", "mtime": 1750442996, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/tenacity/asyncio/__init__.py", "plugin_data": null, "size": 7773, "suppressed": [], "version_id": "1.16.1"}