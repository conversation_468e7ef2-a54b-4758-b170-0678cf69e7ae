{"data_mtime": 1750703628, "dep_lines": [19, 16, 17, 19, 22, 1, 1, 1], "dep_prios": [10, 10, 10, 20, 25, 5, 30, 30], "dependencies": ["tenacity._utils", "abc", "typing", "tenacity", "threading", "builtins", "_frozen_importlib", "datetime"], "hash": "dc34288f55fc3c7165a758e2c7fd56dbcea13f2e", "id": "tenacity.stop", "ignore_all": true, "interface_hash": "3584c3c076ee5e4fc1363e35aacadb035929520f", "mtime": 1750442996, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/tenacity/stop.py", "plugin_data": null, "size": 4113, "suppressed": [], "version_id": "1.16.1"}