{"data_mtime": 1750703628, "dep_lines": [21, 17, 18, 19, 21, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 20, 5, 30, 30], "dependencies": ["tenacity._utils", "abc", "random", "typing", "tenacity", "builtins", "_frozen_importlib", "datetime"], "hash": "0036c3f9aeac6cb95f85c767a35586eb54e1a526", "id": "tenacity.wait", "ignore_all": true, "interface_hash": "1659f3c6b769559ecb833df03363d8d71f3efac2", "mtime": 1750442996, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/tenacity/wait.py", "plugin_data": null, "size": 8049, "suppressed": [], "version_id": "1.16.1"}