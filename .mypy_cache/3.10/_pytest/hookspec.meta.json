{"data_mtime": 1750703633, "dep_lines": [23, 29, 16, 25, 30, 32, 33, 35, 36, 40, 42, 43, 45, 3, 4, 14, 19, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 5, 5, 5, 25, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.argparsing", "_pytest.deprecated", "_pytest.config", "_pytest.fixtures", "_pytest.main", "_pytest.nodes", "_pytest.outcomes", "_pytest.python", "_pytest.reports", "_pytest.runner", "_pytest.terminal", "_pytest.compat", "pathlib", "typing", "pluggy", "pdb", "warnings", "builtins", "_frozen_importlib", "_pytest._code", "_pytest.warning_types", "abc", "bdb", "cmd", "enum", "os", "pluggy._hooks", "pluggy._manager"], "hash": "1cc40af555bbf886a79091ecf27a33244d301542", "id": "_pytest.hookspec", "ignore_all": true, "interface_hash": "b56d637f77222e6000eda90f1de3c4ddf569831b", "mtime": 1749669151, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/_pytest/hookspec.py", "plugin_data": null, "size": 33429, "suppressed": [], "version_id": "1.16.1"}