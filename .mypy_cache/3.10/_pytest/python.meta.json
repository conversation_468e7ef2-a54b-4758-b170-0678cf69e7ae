{"data_mtime": 1750703633, "dep_lines": [38, 42, 58, 69, 34, 35, 36, 41, 43, 55, 59, 66, 67, 73, 75, 80, 82, 83, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 33, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest._io.saferepr", "_pytest.config.argparsing", "_pytest.mark.structures", "_pytest.fixtures", "_pytest.nodes", "_pytest._code", "_pytest._io", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.main", "_pytest.mark", "_pytest.outcomes", "_pytest.pathlib", "_pytest.scope", "_pytest.stash", "_pytest.warning_types", "abc", "dataclasses", "enum", "fnmatch", "inspect", "itertools", "os", "sys", "types", "warnings", "collections", "functools", "pathlib", "typing", "_pytest", "builtins", "_frozen_importlib", "_pytest._io.terminalwriter", "_typeshed", "pluggy", "pluggy._hooks", "pluggy._manager"], "hash": "667fcfd25562375d36363ef4255c8307308cbf78", "id": "_pytest.python", "ignore_all": true, "interface_hash": "25883804a58da0d2dfcae6ef0d518dc0fd07d3b0", "mtime": 1749669151, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/_pytest/python.py", "plugin_data": null, "size": 72986, "suppressed": [], "version_id": "1.16.1"}