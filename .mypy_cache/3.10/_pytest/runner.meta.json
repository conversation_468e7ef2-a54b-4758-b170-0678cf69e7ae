{"data_mtime": 1750703633, "dep_lines": [25, 28, 20, 24, 29, 30, 34, 43, 44, 2, 3, 4, 5, 6, 24, 1, 1, 1, 1, 1, 1, 40], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 25, 25, 10, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 5], "dependencies": ["_pytest._code.code", "_pytest.config.argparsing", "_pytest.reports", "_pytest.timing", "_pytest.deprecated", "_pytest.nodes", "_pytest.outcomes", "_pytest.main", "_pytest.terminal", "bdb", "dataclasses", "os", "sys", "typing", "_pytest", "builtins", "_frozen_importlib", "_pytest._code", "_pytest.config", "_typeshed", "abc"], "hash": "cc856879331314e4ff7e4b589c0a3f2dd2d53ce8", "id": "_pytest.runner", "ignore_all": true, "interface_hash": "b5e32e78e2c5b950ad3d199e793014fca0a3b255", "mtime": 1749669151, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/_pytest/runner.py", "plugin_data": null, "size": 19341, "suppressed": ["exceptiongroup"], "version_id": "1.16.1"}