{"data_mtime": 1750703633, "dep_lines": [22, 33, 34, 35, 37, 40, 1, 2, 3, 4, 5, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 25, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest._io", "_pytest.config", "_pytest.nodes", "_pytest.outcomes", "_pytest.runner", "dataclasses", "os", "io", "pprint", "typing", "builtins", "_frozen_importlib", "_io", "_pytest._code", "_pytest._io.terminalwriter", "_typeshed", "abc", "types"], "hash": "d6c80a4435da44bd5bf27349480908584bc465ef", "id": "_pytest.reports", "ignore_all": true, "interface_hash": "01df9a388ed5c5d7546dab6ca9a8dd629e4a59e4", "mtime": 1749669151, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/_pytest/reports.py", "plugin_data": null, "size": 20944, "suppressed": [], "version_id": "1.16.1"}