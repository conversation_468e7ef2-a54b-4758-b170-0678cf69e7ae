{"data_mtime": 1750703633, "dep_lines": [28, 34, 37, 26, 31, 32, 35, 40, 41, 43, 44, 48, 362, 456, 1, 2, 3, 4, 5, 6, 8, 24, 26, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 20, 20, 10, 10, 5, 10, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.compat", "_pytest.mark.structures", "_pytest._code", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.outcomes", "_pytest.pathlib", "_pytest.stash", "_pytest.warning_types", "_pytest.main", "_pytest.mark", "_pytest.fixtures", "abc", "os", "pathlib", "warnings", "functools", "inspect", "typing", "pluggy", "_pytest", "builtins", "_frozen_importlib", "pluggy._hooks", "types", "typing_extensions"], "hash": "7c441144c51270a16db234a0933259ce19167fc2", "id": "_pytest.nodes", "ignore_all": true, "interface_hash": "5d1f31b67006f6a943a9be346b493459015e740e", "mtime": 1749669151, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/_pytest/nodes.py", "plugin_data": null, "size": 27988, "suppressed": [], "version_id": "1.16.1"}