{"data_mtime": 1750703633, "dep_lines": [40, 42, 43, 48, 36, 37, 38, 39, 41, 44, 51, 53, 58, 533, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 34, 36, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 10, 5, 5, 5, 5, 5, 25, 20, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest._io.wcwidth", "_pytest.assertion.util", "_pytest.config.argparsing", "_pytest._version", "_pytest.nodes", "_pytest.timing", "_pytest._code", "_pytest._io", "_pytest.config", "_pytest.pathlib", "_pytest.reports", "_pytest.main", "_pytest.warnings", "<PERSON><PERSON><PERSON><PERSON>", "dataclasses", "datetime", "inspect", "platform", "sys", "textwrap", "warnings", "collections", "functools", "pathlib", "typing", "pluggy", "_pytest", "builtins", "_collections_abc", "_frozen_importlib", "_pytest._io.terminalwriter", "abc", "enum", "os", "pluggy._hooks", "time", "types", "typing_extensions"], "hash": "ca85006a947bc988b6315898a03894d6aca3d5ec", "id": "_pytest.terminal", "ignore_all": true, "interface_hash": "327266f42c6b5b950f210b3be31e43f1caf8fab3", "mtime": 1749669151, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/_pytest/terminal.py", "plugin_data": null, "size": 54393, "suppressed": [], "version_id": "1.16.1"}