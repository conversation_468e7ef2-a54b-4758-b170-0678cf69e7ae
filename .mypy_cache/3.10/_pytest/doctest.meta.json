{"data_mtime": 1750703633, "dep_lines": [28, 34, 27, 31, 32, 33, 35, 37, 41, 43, 44, 45, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 27, 48, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 20, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.argparsing", "_pytest.outcomes", "_pytest._io", "_pytest.compat", "_pytest.config", "_pytest.fixtures", "_pytest.nodes", "_pytest.pathlib", "_pytest.python", "_pytest.python_api", "_pytest.warning_types", "bdb", "functools", "inspect", "os", "platform", "sys", "traceback", "types", "warnings", "contextlib", "pathlib", "typing", "_pytest", "doctest", "builtins", "_frozen_importlib", "_pytest._code", "_pytest._io.terminalwriter", "_pytest.main", "abc"], "hash": "16684df61d7004c92d6dd2e2f5db4848f67b0c8c", "id": "_pytest.doctest", "ignore_all": true, "interface_hash": "8d192db1313ea2b17f347f1c0f12860dec790a1f", "mtime": 1749669151, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/_pytest/doctest.py", "plugin_data": null, "size": 26345, "suppressed": [], "version_id": "1.16.1"}