{"data_mtime": **********, "dep_lines": [15, 16, 18, 21, 22, 24, 25, 26, 29, 32, 33, 2, 3, 4, 5, 6, 13, 1, 1, 1, 1, 1, 1, 1, 1, 36], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 25], "dependencies": ["_pytest.cacheprovider", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.fixtures", "_pytest.main", "_pytest.monkeypatch", "_pytest.nodes", "_pytest.pytester", "_pytest.terminal", "_pytest.tmpdir", "dataclasses", "shlex", "subprocess", "pathlib", "typing", "iniconfig", "builtins", "_frozen_importlib", "abc", "enum", "os", "pluggy", "pluggy._hooks", "pluggy._manager"], "hash": "e52c591726c97992c38dc64cf9e94777c53c409e", "id": "_pytest.legacypath", "ignore_all": true, "interface_hash": "c1787a3781f98012fbad7a5a9f0c46f0975da9f3", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/_pytest/legacypath.py", "plugin_data": null, "size": 16844, "suppressed": ["pexpect"], "version_id": "1.16.1"}