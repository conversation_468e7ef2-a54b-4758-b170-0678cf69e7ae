{"data_mtime": 1750703633, "dep_lines": [36, 37, 28, 29, 30, 38, 39, 40, 45, 47, 49, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 26, 28, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.argparsing", "_pytest.config.compat", "_pytest._code", "_pytest.nodes", "_pytest.config", "_pytest.fixtures", "_pytest.outcomes", "_pytest.pathlib", "_pytest.reports", "_pytest.runner", "_pytest.warning_types", "<PERSON><PERSON><PERSON><PERSON>", "dataclasses", "fnmatch", "functools", "importlib", "os", "sys", "warnings", "pathlib", "typing", "pluggy", "_pytest", "builtins", "_frozen_importlib", "_pytest.config.exceptions", "_warnings", "abc", "enum", "pluggy._hooks", "pluggy._manager", "pluggy._tracing", "types"], "hash": "12a1f7a8eae5663f244ae523cda8cbf08b0ef211", "id": "_pytest.main", "ignore_all": true, "interface_hash": "f8e48ba4acf000d9b9ed1290cc92279c4cfbdb87", "mtime": 1749669151, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/_pytest/main.py", "plugin_data": null, "size": 35159, "suppressed": [], "version_id": "1.16.1"}