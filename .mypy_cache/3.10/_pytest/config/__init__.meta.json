{"data_mtime": 1750703633, "dep_lines": [51, 52, 54, 71, 73, 3, 8, 48, 49, 50, 57, 58, 60, 66, 67, 72, 397, 1038, 1100, 1375, 1403, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 21, 41, 48, 1370, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 20, 10, 10, 5, 10, 10, 5, 5, 5, 5, 5, 25, 20, 20, 20, 20, 20, 10, 20, 10, 10, 10, 10, 20, 10, 10, 10, 10, 10, 5, 10, 5, 5, 5, 5, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.compat", "_pytest.config.exceptions", "_pytest.config.findpaths", "_pytest._code.code", "_pytest.config.argparsing", "collections.abc", "importlib.metadata", "_pytest._code", "_pytest.deprecated", "_pytest.hookspec", "_pytest._io", "_pytest.outcomes", "_pytest.pathlib", "_pytest.stash", "_pytest.warning_types", "_pytest.terminal", "_pytest.assertion", "_pytest.cacheprovider", "_pytest.helpconfig", "packaging.version", "packaging.requirements", "<PERSON><PERSON><PERSON><PERSON>", "collections", "copy", "dataclasses", "enum", "glob", "importlib", "inspect", "os", "re", "shlex", "sys", "types", "warnings", "functools", "pathlib", "textwrap", "typing", "pluggy", "_pytest", "pytest", "builtins", "_frozen_importlib", "_io", "_pytest._io.terminalwriter", "abc", "io", "pluggy._hooks", "pluggy._manager", "pluggy._tracing", "typing_extensions"], "hash": "8ab41d526497ce3e15fd58e229c11f23f6ce066a", "id": "_pytest.config", "ignore_all": true, "interface_hash": "f42c0653ed6510efa550537f1113006220d445c9", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/_pytest/config/__init__.py", "plugin_data": null, "size": 68520, "suppressed": [], "version_id": "1.16.1"}