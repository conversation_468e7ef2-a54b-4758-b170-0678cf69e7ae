{"data_mtime": 1750703633, "dep_lines": [25, 17, 19, 20, 21, 22, 26, 27, 29, 114, 4, 5, 6, 7, 8, 20, 113, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 10, 10, 10, 5, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.argparsing", "_pytest.pathlib", "_pytest.reports", "_pytest.nodes", "_pytest._io", "_pytest.config", "_pytest.deprecated", "_pytest.fixtures", "_pytest.main", "_pytest.warning_types", "dataclasses", "json", "os", "pathlib", "typing", "_pytest", "warnings", "builtins", "_frozen_importlib", "_typeshed", "abc", "<PERSON><PERSON><PERSON><PERSON>", "enum", "pluggy", "pluggy._hooks", "pluggy._manager", "types"], "hash": "935fc668d353504d5a1e98ba5a420dd6685f4e76", "id": "_pytest.cacheprovider", "ignore_all": true, "interface_hash": "3d88c1471cf0576817cf392792b3f462dcacb4b7", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/_pytest/cacheprovider.py", "plugin_data": null, "size": 21234, "suppressed": [], "version_id": "1.16.1"}