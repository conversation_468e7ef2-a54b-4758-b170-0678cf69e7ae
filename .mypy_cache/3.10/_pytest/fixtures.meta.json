{"data_mtime": 1750703633, "dep_lines": [37, 53, 59, 35, 36, 39, 40, 51, 54, 57, 60, 63, 65, 73, 74, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 34, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 25, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.argparsing", "_pytest.mark.structures", "_pytest.nodes", "_pytest._code", "_pytest._io", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.mark", "_pytest.outcomes", "_pytest.pathlib", "_pytest.scope", "_pytest.main", "_pytest.python", "abc", "dataclasses", "functools", "inspect", "os", "warnings", "collections", "contextlib", "pathlib", "typing", "_pytest", "builtins", "_frozen_importlib", "_pytest._io.terminalwriter", "_pytest.warning_types", "_warnings", "enum", "pluggy", "pluggy._manager", "types", "typing_extensions"], "hash": "e4e72fb4df25127f5ed48c3ab6b7cb4a2c3797b1", "id": "_pytest.fixtures", "ignore_all": true, "interface_hash": "1c70b3a8d61294fb22795cd0b354b543eceda6b1", "mtime": 1749669151, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/_pytest/fixtures.py", "plugin_data": null, "size": 68026, "suppressed": [], "version_id": "1.16.1"}