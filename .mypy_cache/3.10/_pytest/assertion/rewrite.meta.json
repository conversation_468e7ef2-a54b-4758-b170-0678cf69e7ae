{"data_mtime": 1750703633, "dep_lines": [32, 35, 5, 6, 7, 34, 35, 39, 40, 41, 43, 269, 291, 2, 3, 4, 5, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 862, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 293], "dep_prios": [5, 5, 10, 10, 10, 5, 20, 5, 5, 5, 5, 20, 20, 10, 10, 10, 20, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["_pytest._io.saferepr", "_pytest.assertion.util", "importlib.abc", "importlib.machinery", "importlib.util", "_pytest._version", "_pytest.assertion", "_pytest.config", "_pytest.main", "_pytest.pathlib", "_pytest.stash", "_pytest.warning_types", "importlib.readers", "ast", "errno", "functools", "importlib", "io", "itertools", "marshal", "os", "struct", "sys", "tokenize", "types", "collections", "pathlib", "typing", "warnings", "builtins", "_collections_abc", "_frozen_importlib", "_frozen_importlib_external", "_pytest.nodes", "_typeshed", "_warnings", "abc", "importlib._abc", "pluggy", "pluggy._tracing", "posixpath", "typing_extensions"], "hash": "9d861319875b2739b4a77f4bb7b6c431d75b009b", "id": "_pytest.assertion.rewrite", "ignore_all": true, "interface_hash": "c949951f22e6abc6cba380417008161d70c81c0f", "mtime": 1749669151, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/_pytest/assertion/rewrite.py", "plugin_data": null, "size": 47388, "suppressed": ["importlib.resources.readers"], "version_id": "1.16.1"}