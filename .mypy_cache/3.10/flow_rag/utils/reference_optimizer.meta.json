{"data_mtime": 1750705381, "dep_lines": [13, 9, 6, 7, 8, 10, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["flow_rag.infrastructure.log_utils", "concurrent.futures", "typing", "collections", "re", "logging", "dataclasses", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "concurrent", "concurrent.futures._base", "concurrent.futures.thread", "enum", "io", "os", "typing_extensions"], "hash": "c8fd217d868e440a19fed07c7696963a1057a1ab", "id": "flow_rag.utils.reference_optimizer", "ignore_all": false, "interface_hash": "0f691f344feb0e6efe09085644fd3979f81008d1", "mtime": 1750711511, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "flow_rag/utils/reference_optimizer.py", "plugin_data": null, "size": 15330, "suppressed": [], "version_id": "1.16.1"}