{".class": "MypyFile", "_fullname": "flow_rag.application.query_processing", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EmbeddingsPort": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.ports.embeddings.EmbeddingsPort", "kind": "Gdef"}, "LLMPort": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.ports.llm.LLMPort", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "QueryProcessingService": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flow_rag.application.query_processing.QueryProcessingService", "name": "QueryProcessingService", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flow_rag.application.query_processing.QueryProcessingService", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flow_rag.application.query_processing", "mro": ["flow_rag.application.query_processing.QueryProcessingService", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "embeddings_provider", "vector_store", "llm_provider", "logger"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.application.query_processing.QueryProcessingService.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "embeddings_provider", "vector_store", "llm_provider", "logger"], "arg_types": ["flow_rag.application.query_processing.QueryProcessingService", "flow_rag.domain.ports.embeddings.EmbeddingsPort", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "flow_rag.domain.ports.vector_store.VectorStorePort"}, "flow_rag.domain.ports.llm.LLMPort", {".class": "UnionType", "items": ["logging.Logger", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of QueryProcessingService", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_final_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "method_results", "class_results", "special_results"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.application.query_processing.QueryProcessingService._generate_final_context", "name": "_generate_final_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "method_results", "class_results", "special_results"], "arg_types": ["flow_rag.application.query_processing.QueryProcessingService", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_generate_final_context of QueryProcessingService", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_initial_hyde": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.application.query_processing.QueryProcessingService._generate_initial_hyde", "name": "_generate_initial_hyde", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query"], "arg_types": ["flow_rag.application.query_processing.QueryProcessingService", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_generate_initial_hyde of QueryProcessingService", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_perform_search": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "search_query"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.application.query_processing.QueryProcessingService._perform_search", "name": "_perform_search", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "search_query"], "arg_types": ["flow_rag.application.query_processing.QueryProcessingService", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_perform_search of QueryProcessingService", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "class_table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.application.query_processing.QueryProcessingService.class_table", "name": "class_table", "setter_type": null, "type": "builtins.str"}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.application.query_processing.QueryProcessingService.config", "name": "config", "setter_type": null, "type": "flow_rag.infrastructure.config.ConfigManager"}}, "embeddings_provider": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.application.query_processing.QueryProcessingService.embeddings_provider", "name": "embeddings_provider", "setter_type": null, "type": "flow_rag.domain.ports.embeddings.EmbeddingsPort"}}, "hybrid_alpha": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.application.query_processing.QueryProcessingService.hybrid_alpha", "name": "hybrid_alpha", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "hybrid_search_enabled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.application.query_processing.QueryProcessingService.hybrid_search_enabled", "name": "hybrid_search_enabled", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "hyde_enabled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.application.query_processing.QueryProcessingService.hyde_enabled", "name": "hyde_enabled", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "llm_provider": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.application.query_processing.QueryProcessingService.llm_provider", "name": "llm_provider", "setter_type": null, "type": "flow_rag.domain.ports.llm.LLMPort"}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.application.query_processing.QueryProcessingService.logger", "name": "logger", "setter_type": null, "type": {".class": "UnionType", "items": ["logging.Logger", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "max_results": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.application.query_processing.QueryProcessingService.max_results", "name": "max_results", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "uses_pep604_syntax": false}}}, "method_table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.application.query_processing.QueryProcessingService.method_table", "name": "method_table", "setter_type": null, "type": "builtins.str"}}, "process_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.application.query_processing.QueryProcessingService.process_query", "name": "process_query", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query"], "arg_types": ["flow_rag.application.query_processing.QueryProcessingService", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "process_query of QueryProcessingService", "ret_type": "flow_rag.domain.models.QueryResult", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reranking_enabled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.application.query_processing.QueryProcessingService.reranking_enabled", "name": "reranking_enabled", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "reranking_results": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.application.query_processing.QueryProcessingService.reranking_results", "name": "reranking_results", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "uses_pep604_syntax": false}}}, "special_table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.application.query_processing.QueryProcessingService.special_table", "name": "special_table", "setter_type": null, "type": "builtins.str"}}, "vector_store": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.application.query_processing.QueryProcessingService.vector_store", "name": "vector_store", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "flow_rag.domain.ports.vector_store.VectorStorePort"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flow_rag.application.query_processing.QueryProcessingService.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flow_rag.application.query_processing.QueryProcessingService", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QueryResult": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.models.QueryResult", "kind": "Gdef"}, "ThreadPoolExecutor": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures.thread.ThreadPoolExecutor", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "VectorStorePort": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.ports.vector_store.VectorStorePort", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.application.query_processing.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.application.query_processing.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.application.query_processing.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.application.query_processing.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.application.query_processing.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.application.query_processing.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "log_message": {".class": "SymbolTableNode", "cross_ref": "flow_rag.infrastructure.log_utils.log_message", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}, "truncate_context_by_tokens": {".class": "SymbolTableNode", "cross_ref": "flow_rag.application.utils.query_utils.truncate_context_by_tokens", "kind": "Gdef"}}, "path": "flow_rag/application/query_processing.py"}