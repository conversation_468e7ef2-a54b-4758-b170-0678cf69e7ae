{"data_mtime": 1750705382, "dep_lines": [9, 10, 11, 3, 8, 12, 39, 5, 1, 2, 4, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 5, 5, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["flow_rag.domain.ports.embeddings", "flow_rag.domain.ports.vector_store", "flow_rag.domain.ports.llm", "flow_rag.infrastructure.log_utils", "flow_rag.domain.models", "flow_rag.application.utils", "flow_rag.infrastructure.config", "concurrent.futures", "typing", "logging", "time", "traceback", "builtins", "_frozen_importlib", "abc", "concurrent", "concurrent.futures._base", "concurrent.futures.thread", "flow_rag.application.utils.query_utils", "flow_rag.domain", "flow_rag.domain.ports", "flow_rag.infrastructure", "typing_extensions"], "hash": "7ddb9bc16f89016d18500ad1883dbc3a1c8e7934", "id": "flow_rag.application.query_processing", "ignore_all": false, "interface_hash": "6b018c0f76cd7e94913dcb02c47c62a06cb278b5", "mtime": 1750711511, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "flow_rag/application/query_processing.py", "plugin_data": null, "size": 16088, "suppressed": [], "version_id": "1.16.1"}