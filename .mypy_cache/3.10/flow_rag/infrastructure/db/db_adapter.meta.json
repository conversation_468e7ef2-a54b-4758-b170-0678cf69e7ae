{"data_mtime": 1750703623, "dep_lines": [6, 1, 2, 3, 4, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["flow_rag.infrastructure.log_utils", "os", "sqlite3", "threading", "typing", "logging", "traceback", "builtins", "_frozen_importlib", "_io", "_sqlite3", "_thread", "_typeshed", "abc", "genericpath", "io", "posixpath", "typing_extensions"], "hash": "987d55f6b0d1db3651ad937be12991c2c769d838", "id": "flow_rag.infrastructure.db.db_adapter", "ignore_all": false, "interface_hash": "f130785bc0486da066b9ad46a1025c40ed4147d6", "mtime": 1750711511, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "flow_rag/infrastructure/db/db_adapter.py", "plugin_data": null, "size": 8173, "suppressed": [], "version_id": "1.16.1"}