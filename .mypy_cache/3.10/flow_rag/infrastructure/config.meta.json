{"data_mtime": 1750703620, "dep_lines": [1, 2, 3, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["os", "typing", "pathlib", "builtins", "_frozen_importlib", "_typeshed", "abc", "types", "typing_extensions"], "hash": "4f1a6306267ec1b79eca1e40ae66eab20d24b878", "id": "flow_rag.infrastructure.config", "ignore_all": false, "interface_hash": "a5d8355b530c6aa466ede2832b8449192660805b", "mtime": 1750711905, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "flow_rag/infrastructure/config.py", "plugin_data": null, "size": 6223, "suppressed": [], "version_id": "1.16.1"}