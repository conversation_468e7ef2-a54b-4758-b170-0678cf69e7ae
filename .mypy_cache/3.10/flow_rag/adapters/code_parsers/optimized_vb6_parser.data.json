{".class": "MypyFile", "_fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseCodeParser": {".class": "SymbolTableNode", "cross_ref": "flow_rag.adapters.code_parsers.base_parser.BaseCodeParser", "kind": "Gdef"}, "Class": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.models.Class", "kind": "Gdef"}, "CodeParserPort": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.ports.code_parser.CodeParserPort", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FILE_FILTERS": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.ports.file_loader.FILE_FILTERS", "kind": "Gdef"}, "LanguageEnum": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.ports.file_loader.LanguageEnum", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Method": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.models.Method", "kind": "Gdef"}, "OptimizedVB6CodeParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["flow_rag.adapters.code_parsers.base_parser.BaseCodeParser", "flow_rag.domain.ports.code_parser.CodeParserPort"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser", "name": "OptimizedVB6CodeParser", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "flow_rag.adapters.code_parsers.optimized_vb6_parser", "mro": ["flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser", "flow_rag.adapters.code_parsers.base_parser.BaseCodeParser", "flow_rag.domain.ports.code_parser.CodeParserPort", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "logger"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "logger"], "arg_types": ["flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser", {".class": "UnionType", "items": ["logging.Logger", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of OptimizedVB6CodeParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_method_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "content", "start_pos", "start_line"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser._find_method_end", "name": "_find_method_end", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "content", "start_pos", "start_line"], "arg_types": ["flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser", "builtins.str", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_find_method_end of OptimizedVB6CodeParser", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_references_in_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "file_path", "class_names_set", "method_names_set", "references"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser._find_references_in_file", "name": "_find_references_in_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "file_path", "class_names_set", "method_names_set", "references"], "arg_types": ["flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "collections.defaultdict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_find_references_in_file of OptimizedVB6CodeParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_method_reference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "line", "method_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser._is_method_reference", "name": "_is_method_reference", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "line", "method_name"], "arg_types": ["flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_is_method_reference of OptimizedVB6CodeParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_single_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser._parse_single_file", "name": "_parse_single_file", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file_path"], "arg_types": ["flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_parse_single_file of OptimizedVB6CodeParser", "ret_type": {".class": "Instance", "args": ["flow_rag.domain.models.Method"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "end_pattern": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser.end_pattern", "name": "end_pattern", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "find_references": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "file_list", "class_names", "method_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser.find_references", "name": "find_references", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "file_list", "class_names", "method_names"], "arg_types": ["flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "flow_rag.domain.ports.file_loader.LanguageEnum"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_references of OptimizedVB6CodeParser", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "function_pattern": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser.function_pattern", "name": "function_pattern", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "get_language_from_extension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file_ext"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser.get_language_from_extension", "name": "get_language_from_extension", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file_ext"], "arg_types": ["flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_language_from_extension of OptimizedVB6CodeParser", "ret_type": {".class": "UnionType", "items": ["flow_rag.domain.ports.file_loader.LanguageEnum", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_supported_languages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser.get_supported_languages", "name": "get_supported_languages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_supported_languages of OptimizedVB6CodeParser", "ret_type": {".class": "Instance", "args": ["flow_rag.domain.ports.file_loader.LanguageEnum"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_code_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file_list"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser.parse_code_files", "name": "parse_code_files", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file_list"], "arg_types": ["flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "flow_rag.domain.ports.file_loader.LanguageEnum"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_code_files of OptimizedVB6CodeParser", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["flow_rag.domain.models.Class"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["flow_rag.domain.models.Method"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser.set_defaults", "name": "set_defaults", "type": null}}, "should_ignore_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser.should_ignore_path", "name": "should_ignore_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "should_ignore_path of OptimizedVB6CodeParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Reference": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.models.Reference", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "VB6_FILE_FILTERS": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.ports.file_loader.VB6_FILE_FILTERS", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.optimized_vb6_parser.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "log_message": {".class": "SymbolTableNode", "cross_ref": "flow_rag.infrastructure.log_utils.log_message", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}}, "path": "flow_rag/adapters/code_parsers/optimized_vb6_parser.py"}