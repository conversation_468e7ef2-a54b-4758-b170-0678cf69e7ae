{".class": "MypyFile", "_fullname": "flow_rag.adapters.code_parsers.factory", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CodeParserFactory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flow_rag.adapters.code_parsers.factory.CodeParserFactory", "name": "CodeParserFactory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flow_rag.adapters.code_parsers.factory.CodeParserFactory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flow_rag.adapters.code_parsers.factory", "mro": ["flow_rag.adapters.code_parsers.factory.CodeParserFactory", "builtins.object"], "names": {".class": "SymbolTable", "create_parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["language", "logger"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "flow_rag.adapters.code_parsers.factory.CodeParserFactory.create_parser", "name": "create_parser", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["language", "logger"], "arg_types": ["flow_rag.domain.ports.file_loader.LanguageEnum", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_parser of CodeParserFactory", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "flow_rag.adapters.code_parsers.factory.CodeParserFactory.create_parser", "name": "create_parser", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["language", "logger"], "arg_types": ["flow_rag.domain.ports.file_loader.LanguageEnum", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_parser of CodeParserFactory", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flow_rag.adapters.code_parsers.factory.CodeParserFactory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flow_rag.adapters.code_parsers.factory.CodeParserFactory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LanguageEnum": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.ports.file_loader.LanguageEnum", "kind": "Gdef"}, "OptimizedVB6CodeParser": {".class": "SymbolTableNode", "cross_ref": "flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "TREE_SITTER_FILE_FILTERS": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.ports.file_loader.TREE_SITTER_FILE_FILTERS", "kind": "Gdef"}, "TreesitterCodeParser": {".class": "SymbolTableNode", "cross_ref": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser", "kind": "Gdef"}, "VB6_FILE_FILTERS": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.ports.file_loader.VB6_FILE_FILTERS", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.factory.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.factory.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.factory.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.factory.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.factory.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.factory.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "get_main_extension": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.ports.file_loader.get_main_extension", "kind": "Gdef"}, "log_message": {".class": "SymbolTableNode", "cross_ref": "flow_rag.infrastructure.log_utils.log_message", "kind": "Gdef"}}, "path": "flow_rag/adapters/code_parsers/factory.py"}