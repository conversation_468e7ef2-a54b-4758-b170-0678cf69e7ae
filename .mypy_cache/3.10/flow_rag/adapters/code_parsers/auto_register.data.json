{".class": "MypyFile", "_fullname": "flow_rag.adapters.code_parsers.auto_register", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "LanguageEnum": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.ports.file_loader.LanguageEnum", "kind": "Gdef"}, "OptimizedVB6CodeParser": {".class": "SymbolTableNode", "cross_ref": "flow_rag.adapters.code_parsers.optimized_vb6_parser.OptimizedVB6CodeParser", "kind": "Gdef"}, "ParserRegistry": {".class": "SymbolTableNode", "cross_ref": "flow_rag.adapters.code_parsers.registry.ParserRegistry", "kind": "Gdef"}, "TreesitterCodeParser": {".class": "SymbolTableNode", "cross_ref": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.auto_register.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.auto_register.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.auto_register.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.auto_register.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.auto_register.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.auto_register.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "flow_rag/adapters/code_parsers/auto_register.py"}