{"data_mtime": 1750705746, "dep_lines": [8, 9, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30, 30], "dependencies": ["flow_rag.adapters.code_parsers.tree_sitter_parser", "flow_rag.adapters.code_parsers.optimized_vb6_parser", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "7a0139b13dec8ccd6acb4aba72fc282a21672991", "id": "flow_rag.adapters.code_parsers", "ignore_all": false, "interface_hash": "79de58bdf39bbdc619d55c6f3a612da6bedf5594", "mtime": 1749496089, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "flow_rag/adapters/code_parsers/__init__.py", "plugin_data": null, "size": 361, "suppressed": [], "version_id": "1.16.1"}