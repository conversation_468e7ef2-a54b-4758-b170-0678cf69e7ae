{".class": "MypyFile", "_fullname": "flow_rag.adapters.code_parsers.base_parser", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseCodeParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["find_references", 1], ["parse_code_files", 1]], "alt_promote": null, "bases": ["flow_rag.domain.ports.code_parser.CodeParserPort"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flow_rag.adapters.code_parsers.base_parser.BaseCodeParser", "name": "BaseCodeParser", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "flow_rag.adapters.code_parsers.base_parser.BaseCodeParser", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "flow_rag.adapters.code_parsers.base_parser", "mro": ["flow_rag.adapters.code_parsers.base_parser.BaseCodeParser", "flow_rag.domain.ports.code_parser.CodeParserPort", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "BLACKLIST_DIR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "flow_rag.adapters.code_parsers.base_parser.BaseCodeParser.BLACKLIST_DIR", "name": "BLACKLIST_DIR", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "BLACKLIST_FILES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "flow_rag.adapters.code_parsers.base_parser.BaseCodeParser.BLACKLIST_FILES", "name": "BLACKLIST_FILES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "WHITELIST_FILES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "flow_rag.adapters.code_parsers.base_parser.BaseCodeParser.WHITELIST_FILES", "name": "WHITELIST_FILES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "logger"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.base_parser.BaseCodeParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "logger"], "arg_types": ["flow_rag.adapters.code_parsers.base_parser.BaseCodeParser", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BaseCodeParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_load_gitignore_patterns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "codebase_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.base_parser.BaseCodeParser._load_gitignore_patterns", "name": "_load_gitignore_patterns", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "codebase_path"], "arg_types": ["flow_rag.adapters.code_parsers.base_parser.BaseCodeParser", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_load_gitignore_patterns of BaseCodeParser", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_language_from_extension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file_ext"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.base_parser.BaseCodeParser.get_language_from_extension", "name": "get_language_from_extension", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file_ext"], "arg_types": ["flow_rag.adapters.code_parsers.base_parser.BaseCodeParser", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_language_from_extension of BaseCodeParser", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_supported_languages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.base_parser.BaseCodeParser.get_supported_languages", "name": "get_supported_languages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flow_rag.adapters.code_parsers.base_parser.BaseCodeParser"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_supported_languages of BaseCodeParser", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gitignore_patterns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "flow_rag.adapters.code_parsers.base_parser.BaseCodeParser.gitignore_patterns", "name": "gitignore_patterns", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "load_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "codebase_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.base_parser.BaseCodeParser.load_files", "name": "load_files", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "codebase_path"], "arg_types": ["flow_rag.adapters.code_parsers.base_parser.BaseCodeParser", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "load_files of BaseCodeParser", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.base_parser.BaseCodeParser.logger", "name": "logger", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "set_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.base_parser.BaseCodeParser.set_defaults", "name": "set_defaults", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flow_rag.adapters.code_parsers.base_parser.BaseCodeParser"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_defaults of BaseCodeParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "should_ignore_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.base_parser.BaseCodeParser.should_ignore_path", "name": "should_ignore_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["flow_rag.adapters.code_parsers.base_parser.BaseCodeParser", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "should_ignore_path of BaseCodeParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flow_rag.adapters.code_parsers.base_parser.BaseCodeParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flow_rag.adapters.code_parsers.base_parser.BaseCodeParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CodeParserPort": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.ports.code_parser.CodeParserPort", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.base_parser.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.base_parser.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.base_parser.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.base_parser.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.base_parser.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.base_parser.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "detect_language_from_extension": {".class": "SymbolTableNode", "cross_ref": "flow_rag.adapters.file_loader.utils.detect_language_from_extension", "kind": "Gdef"}}, "path": "flow_rag/adapters/code_parsers/base_parser.py"}