{"data_mtime": 1750709599, "dep_lines": [3, 2, 1, 39, 40, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["flow_rag.domain.ports.code_parser", "flow_rag.adapters.file_loader", "typing", "os", "fnmatch", "builtins", "_frozen_importlib", "_io", "abc", "enum", "flow_rag.adapters.file_loader.utils", "flow_rag.domain", "flow_rag.domain.ports", "flow_rag.domain.ports.file_loader", "io"], "hash": "6ae288b34576da8a38716cea7624a57a6a5f8ab7", "id": "flow_rag.adapters.code_parsers.base_parser", "ignore_all": false, "interface_hash": "ba4b38d6d1e7e26f3daddab404e84cb58c989758", "mtime": 1750707615, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "flow_rag/adapters/code_parsers/base_parser.py", "plugin_data": null, "size": 3401, "suppressed": [], "version_id": "1.16.1"}