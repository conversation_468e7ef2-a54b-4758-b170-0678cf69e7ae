{".class": "MypyFile", "_fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseCodeParser": {".class": "SymbolTableNode", "cross_ref": "flow_rag.adapters.code_parsers.base_parser.BaseCodeParser", "kind": "Gdef"}, "Class": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.models.Class", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FILE_FILTERS": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.ports.file_loader.FILE_FILTERS", "kind": "Gdef"}, "LanguageEnum": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.ports.file_loader.LanguageEnum", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Method": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.models.Method", "kind": "Gdef"}, "Node": {".class": "SymbolTableNode", "cross_ref": "tree_sitter.Node", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Reference": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.models.Reference", "kind": "Gdef"}, "TREE_SITTER_FILE_FILTERS": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.ports.file_loader.TREE_SITTER_FILE_FILTERS", "kind": "Gdef"}, "TreesitterClassNode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterClassNode", "name": "TreesitterClassNode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterClassNode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flow_rag.adapters.code_parsers.tree_sitter_parser", "mro": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterClassNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "name", "method_declarations", "node", "logger"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterClassNode.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "name", "method_declarations", "node", "logger"], "arg_types": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterClassNode", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, "tree_sitter.Node", "logging.Logger"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TreesitterClassNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterClassNode.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "method_declarations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterClassNode.method_declarations", "name": "method_declarations", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterClassNode.name", "name": "name", "setter_type": null, "type": "builtins.str"}}, "node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterClassNode.node", "name": "node", "setter_type": null, "type": "tree_sitter.Node"}}, "source_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterClassNode.source_code", "name": "source_code", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterClassNode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterClassNode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TreesitterCodeParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["flow_rag.adapters.code_parsers.base_parser.BaseCodeParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser", "name": "Treesitter<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "flow_rag.adapters.code_parsers.tree_sitter_parser", "mro": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser", "flow_rag.adapters.code_parsers.base_parser.BaseCodeParser", "flow_rag.domain.ports.code_parser.CodeParserPort", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "NODE_TYPES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser.NODE_TYPES", "name": "NODE_TYPES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "REFERENCE_IDENTIFIERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser.REFERENCE_IDENTIFIERS", "name": "REFERENCE_IDENTIFIERS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "logger"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "logger"], "arg_types": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser", {".class": "UnionType", "items": ["logging.Logger", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TreesitterCodeParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_parser_for_language": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser._get_parser_for_language", "name": "_get_parser_for_language", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "language"], "arg_types": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser", "flow_rag.domain.ports.file_loader.LanguageEnum"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_parser_for_language of TreesitterCodeParser", "ret_type": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_load_gitignore_patterns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "codebase_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser._load_gitignore_patterns", "name": "_load_gitignore_patterns", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "codebase_path"], "arg_types": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_load_gitignore_patterns of TreesitterCodeParser", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tree"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser.find_classes", "name": "find_classes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tree"], "arg_types": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_classes of TreesitterCodeParser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_methods": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tree"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser.find_methods", "name": "find_methods", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tree"], "arg_types": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_methods of TreesitterCodeParser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_references": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "file_list", "class_names", "method_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser.find_references", "name": "find_references", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "file_list", "class_names", "method_names"], "arg_types": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "flow_rag.domain.ports.file_loader.LanguageEnum"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_references of TreesitterCodeParser", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_supported_languages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser.get_supported_languages", "name": "get_supported_languages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_supported_languages of TreesitterCodeParser", "ret_type": {".class": "Instance", "args": ["flow_rag.domain.ports.file_loader.LanguageEnum"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse of TreesitterCodeParser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_code_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file_list"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser.parse_code_files", "name": "parse_code_files", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file_list"], "arg_types": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "flow_rag.domain.ports.file_loader.LanguageEnum"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_code_files of TreesitterCodeParser", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["flow_rag.domain.models.Class"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["flow_rag.domain.models.Method"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parsers_by_language": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser.parsers_by_language", "name": "parsers_by_language", "setter_type": null, "type": {".class": "Instance", "args": ["flow_rag.domain.ports.file_loader.LanguageEnum", "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "set_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser.set_defaults", "name": "set_defaults", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_defaults of TreesitterCodeParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "should_ignore_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser.should_ignore_path", "name": "should_ignore_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "should_ignore_path of TreesitterCodeParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterCodeParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TreesitterMethodNode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterMethodNode", "name": "TreesitterMethodNode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterMethodNode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flow_rag.adapters.code_parsers.tree_sitter_parser", "mro": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterMethodNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "doc_comment", "method_source_code", "node", "class_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterMethodNode.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "doc_comment", "method_source_code", "node", "class_name"], "arg_types": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterMethodNode", "builtins.str", "builtins.str", "builtins.str", "tree_sitter.Node", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TreesitterMethodNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "class_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterMethodNode.class_name", "name": "class_name", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "doc_comment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterMethodNode.doc_comment", "name": "doc_comment", "setter_type": null, "type": "builtins.str"}}, "method_source_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterMethodNode.method_source_code", "name": "method_source_code", "setter_type": null, "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterMethodNode.name", "name": "name", "setter_type": null, "type": "builtins.str"}}, "node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterMethodNode.node", "name": "node", "setter_type": null, "type": "tree_sitter.Node"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterMethodNode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterMethodNode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TreesitterParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flow_rag.adapters.code_parsers.tree_sitter_parser", "mro": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser", "builtins.object"], "names": {".class": "SymbolTable", "LANGUAGE_QUERIES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser.LANGUAGE_QUERIES", "name": "LANGUAGE_QUERIES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "language"], "arg_types": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser", "flow_rag.domain.ports.file_loader.LanguageEnum"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TreesitterPars<PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_doc_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser._extract_doc_comment", "name": "_extract_doc_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser", "tree_sitter.Node"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_extract_doc_comment of TreesitterPars<PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_methods_in_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "class_node"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser._extract_methods_in_class", "name": "_extract_methods_in_class", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "class_node"], "arg_types": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser", "tree_sitter.Node"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_extract_methods_in_class of TreesitterParser", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_descendant_of": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "ancestor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser._is_descendant_of", "name": "_is_descendant_of", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "ancestor"], "arg_types": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser", "tree_sitter.Node", "tree_sitter.Node"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_is_descendant_of of Treesitter<PERSON><PERSON><PERSON>", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "class_query": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser.class_query", "name": "class_query", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "flow_rag.adapters.code_parsers.tree_sitter_parser.get_language", "source_any": {".class": "AnyType", "missing_import_name": "flow_rag.adapters.code_parsers.tree_sitter_parser.get_language", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "doc_query": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser.doc_query", "name": "doc_query", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "flow_rag.adapters.code_parsers.tree_sitter_parser.get_language", "source_any": {".class": "AnyType", "missing_import_name": "flow_rag.adapters.code_parsers.tree_sitter_parser.get_language", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "language_enum": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser.language_enum", "name": "language_enum", "setter_type": null, "type": "flow_rag.domain.ports.file_loader.LanguageEnum"}}, "language_obj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser.language_obj", "name": "language_obj", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "flow_rag.adapters.code_parsers.tree_sitter_parser.get_language", "source_any": {".class": "AnyType", "missing_import_name": "flow_rag.adapters.code_parsers.tree_sitter_parser.get_language", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "method_query": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser.method_query", "name": "method_query", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "flow_rag.adapters.code_parsers.tree_sitter_parser.get_language", "source_any": {".class": "AnyType", "missing_import_name": "flow_rag.adapters.code_parsers.tree_sitter_parser.get_language", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file_bytes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file_bytes"], "arg_types": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser", "builtins.bytes"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse of TreesitterParser", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterClassNode"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterMethodNode"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser.parser", "name": "parser", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "flow_rag.adapters.code_parsers.tree_sitter_parser.get_parser", "source_any": {".class": "AnyType", "missing_import_name": "flow_rag.adapters.code_parsers.tree_sitter_parser.get_parser", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "query_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser.query_config", "name": "query_config", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flow_rag.adapters.code_parsers.tree_sitter_parser.TreesitterParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "fnmatch": {".class": "SymbolTableNode", "cross_ref": "fnmatch", "kind": "Gdef"}, "get_language": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.get_language", "name": "get_language", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "flow_rag.adapters.code_parsers.tree_sitter_parser.get_language", "source_any": null, "type_of_any": 3}}}, "get_parser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "flow_rag.adapters.code_parsers.tree_sitter_parser.get_parser", "name": "get_parser", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "flow_rag.adapters.code_parsers.tree_sitter_parser.get_parser", "source_any": null, "type_of_any": 3}}}, "log_message": {".class": "SymbolTableNode", "cross_ref": "flow_rag.infrastructure.log_utils.log_message", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "flow_rag/adapters/code_parsers/tree_sitter_parser.py"}