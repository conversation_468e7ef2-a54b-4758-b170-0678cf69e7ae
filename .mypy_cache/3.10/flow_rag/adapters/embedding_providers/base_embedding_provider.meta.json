{"data_mtime": 1750705382, "dep_lines": [16, 11, 12, 15, 7, 8, 9, 10, 13, 14, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 17], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["flow_rag.domain.ports.embeddings", "flow_rag.infrastructure.log_utils", "flow_rag.infrastructure.config", "flow_rag.domain.models", "abc", "datetime", "typing", "logging", "tiktoken", "traceback", "json", "builtins", "_frozen_importlib", "_typeshed", "flow_rag.domain", "flow_rag.domain.ports", "flow_rag.infrastructure", "json.decoder", "tiktoken.core", "types", "typing_extensions"], "hash": "763e9fe4f486d168cdbee5ab5408a864e4eea1e0", "id": "flow_rag.adapters.embedding_providers.base_embedding_provider", "ignore_all": false, "interface_hash": "40bf66ae670f80c53bd6236a170a422f29d6f552", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "flow_rag/adapters/embedding_providers/base_embedding_provider.py", "plugin_data": null, "size": 29732, "suppressed": ["tqdm"], "version_id": "1.16.1"}