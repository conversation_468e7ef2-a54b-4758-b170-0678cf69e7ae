{"data_mtime": **********, "dep_lines": [13, 12, 14, 7, 8, 9, 10, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["flow_rag.adapters.embedding_providers.base_embedding_provider", "flow_rag.infrastructure.config", "flow_rag.infrastructure.log_utils", "typing", "logging", "requests", "time", "traceback", "builtins", "_frozen_importlib", "_typeshed", "abc", "flow_rag.domain", "flow_rag.domain.ports", "flow_rag.domain.ports.embeddings", "flow_rag.infrastructure", "http", "http.cookiejar", "json", "json.decoder", "requests.auth", "requests.cookies", "requests.exceptions", "requests.models", "requests.sessions", "types", "typing_extensions"], "hash": "fe5c5b24344af8040e9bc823d5f4277e3194c0cc", "id": "flow_rag.adapters.embedding_providers.jina_embeddings", "ignore_all": false, "interface_hash": "e0b8d507a3e5db0092466d799fefb91ef9151963", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "flow_rag/adapters/embedding_providers/jina_embeddings.py", "plugin_data": null, "size": 8756, "suppressed": [], "version_id": "1.16.1"}