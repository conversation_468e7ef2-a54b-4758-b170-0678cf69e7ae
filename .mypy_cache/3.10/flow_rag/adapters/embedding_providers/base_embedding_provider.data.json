{".class": "MypyFile", "_fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseEmbeddingProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_get_embeddings", 1], ["_setup_client", 1], ["get_embedding_dimensions", 1], ["max_token_limit", 1], ["model_name", 1]], "alt_promote": null, "bases": ["flow_rag.domain.ports.embeddings.EmbeddingsPort", "abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider", "name": "BaseEmbeddingProvider", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "flow_rag.adapters.embedding_providers.base_embedding_provider", "mro": ["flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider", "flow_rag.domain.ports.embeddings.EmbeddingsPort", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "config", "logger", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "config", "logger", "kwargs"], "arg_types": ["flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["logging.Logger", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BaseEmbeddingProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_class_embedding_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "class_obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider._create_class_embedding_text", "name": "_create_class_embedding_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "class_obj"], "arg_types": ["flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider", "flow_rag.domain.models.Class"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_create_class_embedding_text of BaseEmbeddingProvider", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_method_embedding_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "method"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider._create_method_embedding_text", "name": "_create_method_embedding_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "method"], "arg_types": ["flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider", "flow_rag.domain.models.Method"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_create_method_embedding_text of BaseEmbeddingProvider", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_config_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider._get_config_manager", "name": "_get_config_manager", "type": null}}, "_get_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "texts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider._get_embeddings", "name": "_get_embeddings", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "texts"], "arg_types": ["flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_embeddings of BaseEmbeddingProvider", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider._get_embeddings", "name": "_get_embeddings", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "texts"], "arg_types": ["flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_embeddings of BaseEmbeddingProvider", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_query_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider._query_cache", "name": "_query_cache", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_setup_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider._setup_client", "name": "_setup_client", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider._setup_client", "name": "_setup_client", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_setup_client of BaseEmbeddingProvider", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_split_text_into_chunks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "text", "max_tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider._split_text_into_chunks", "name": "_split_text_into_chunks", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "text", "max_tokens"], "arg_types": ["flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider", "builtins.str", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_split_text_into_chunks of BaseEmbeddingProvider", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tokenizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider._tokenizer", "name": "_tokenizer", "setter_type": null, "type": {".class": "NoneType"}}}, "batch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider.batch_size", "name": "batch_size", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "clip_text_to_max_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider.clip_text_to_max_tokens", "name": "clip_text_to_max_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "clip_text_to_max_tokens of BaseEmbeddingProvider", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "embed_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "classes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider.embed_classes", "name": "embed_classes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "classes"], "arg_types": ["flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider", {".class": "Instance", "args": ["flow_rag.domain.models.Class"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_classes of BaseEmbeddingProvider", "ret_type": {".class": "Instance", "args": ["flow_rag.domain.models.EmbeddedClass"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "embed_methods": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "methods"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider.embed_methods", "name": "embed_methods", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "methods"], "arg_types": ["flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider", {".class": "Instance", "args": ["flow_rag.domain.models.Method"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_methods of BaseEmbeddingProvider", "ret_type": {".class": "Instance", "args": ["flow_rag.domain.models.EmbeddedMethod"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "embed_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider.embed_query", "name": "embed_query", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query"], "arg_types": ["flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_query of BaseEmbeddingProvider", "ret_type": {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "embed_special_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "special_files", "batch_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider.embed_special_files", "name": "embed_special_files", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "special_files", "batch_size"], "arg_types": ["flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_special_files of BaseEmbeddingProvider", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_class_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider.get_class_schema", "name": "get_class_schema", "type": null}}, "get_method_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider.get_method_schema", "name": "get_method_schema", "type": null}}, "get_special_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider.get_special_schema", "name": "get_special_schema", "type": null}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "max_tokens": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider.max_tokens", "name": "max_tokens", "setter_type": null, "type": "builtins.int"}}, "tokenizer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider.tokenizer", "name": "tokenizer", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider.tokenizer", "name": "tokenizer", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "tokenizer of BaseEmbeddingProvider", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Class": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.models.Class", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EmbeddedClass": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.models.EmbeddedClass", "kind": "Gdef"}, "EmbeddedMethod": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.models.EmbeddedMethod", "kind": "Gdef"}, "EmbeddingsPort": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.ports.embeddings.EmbeddingsPort", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Method": {".class": "SymbolTableNode", "cross_ref": "flow_rag.domain.models.Method", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "get_config": {".class": "SymbolTableNode", "cross_ref": "flow_rag.infrastructure.config.get_config", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "log_message": {".class": "SymbolTableNode", "cross_ref": "flow_rag.infrastructure.log_utils.log_message", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "tiktoken": {".class": "SymbolTableNode", "cross_ref": "tiktoken", "kind": "Gdef"}, "tqdm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "flow_rag.adapters.embedding_providers.base_embedding_provider.tqdm", "name": "tqdm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "flow_rag.adapters.embedding_providers.base_embedding_provider.tqdm", "source_any": null, "type_of_any": 3}}}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}}, "path": "flow_rag/adapters/embedding_providers/base_embedding_provider.py"}