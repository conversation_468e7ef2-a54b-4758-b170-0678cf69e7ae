{".class": "MypyFile", "_fullname": "flow_rag.adapters.embedding_providers.jina_embeddings", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseEmbeddingProvider": {".class": "SymbolTableNode", "cross_ref": "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "JinaEmbeddingProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "flow_rag.adapters.embedding_providers.jina_embeddings", "mro": ["flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider", "flow_rag.adapters.embedding_providers.base_embedding_provider.BaseEmbeddingProvider", "flow_rag.domain.ports.embeddings.EmbeddingsPort", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "config", "logger", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "config", "logger", "kwargs"], "arg_types": ["flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["logging.Logger", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Jina<PERSON>Provider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_config_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider._get_config_manager", "name": "_get_config_manager", "type": null}}, "_get_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "texts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider._get_embeddings", "name": "_get_embeddings", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "texts"], "arg_types": ["flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_embeddings of JinaEmbeddingProvider", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_error_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider._handle_error_response", "name": "_handle_error_response", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider", "requests.models.Response"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_handle_error_response of <PERSON><PERSON>Provider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_max_token_limit": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider._max_token_limit", "name": "_max_token_limit", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_model_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider._model_name", "name": "_model_name", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_setup_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider._setup_client", "name": "_setup_client", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_setup_client of JinaEmbeddingProvider", "ret_type": "requests.sessions.Session", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "api_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider.api_key", "name": "api_key", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "base_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider.base_url", "name": "base_url", "setter_type": null, "type": "builtins.str"}}, "embedding_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider.embedding_dim", "name": "embedding_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "get_embedding_dimensions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider.get_embedding_dimensions", "name": "get_embedding_dimensions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_embedding_dimensions of JinaEmbeddingProvider", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "max_token_limit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider.max_token_limit", "name": "max_token_limit", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "max_token_limit of JinaEmbeddingProvider", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider.model_name", "name": "model_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_name of <PERSON><PERSON><PERSON>Provider", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider.model_name", "name": "model_name", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_name of <PERSON><PERSON><PERSON>Provider", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider.session", "name": "session", "setter_type": null, "type": {".class": "UnionType", "items": ["requests.sessions.Session", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flow_rag.adapters.embedding_providers.jina_embeddings.JinaEmbeddingProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.embedding_providers.jina_embeddings.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "get_config": {".class": "SymbolTableNode", "cross_ref": "flow_rag.infrastructure.config.get_config", "kind": "Gdef"}, "log_message": {".class": "SymbolTableNode", "cross_ref": "flow_rag.infrastructure.log_utils.log_message", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "requests": {".class": "SymbolTableNode", "cross_ref": "requests", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}}, "path": "flow_rag/adapters/embedding_providers/jina_embeddings.py"}