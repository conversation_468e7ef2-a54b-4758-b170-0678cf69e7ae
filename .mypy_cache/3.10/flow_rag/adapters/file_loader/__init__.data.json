{".class": "MypyFile", "_fullname": "flow_rag.adapters.file_loader", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "FileLoader": {".class": "SymbolTableNode", "cross_ref": "flow_rag.adapters.file_loader.loader.FileLoader", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "flow_rag.adapters.file_loader.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.file_loader.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.file_loader.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.file_loader.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.file_loader.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.file_loader.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.file_loader.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flow_rag.adapters.file_loader.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "detect_language_from_extension": {".class": "SymbolTableNode", "cross_ref": "flow_rag.adapters.file_loader.utils.detect_language_from_extension", "kind": "Gdef"}, "get_file_language": {".class": "SymbolTableNode", "cross_ref": "flow_rag.adapters.file_loader.utils.get_file_language", "kind": "Gdef"}, "get_file_language_string": {".class": "SymbolTableNode", "cross_ref": "flow_rag.adapters.file_loader.utils.get_file_language_string", "kind": "Gdef"}, "load_files_generic": {".class": "SymbolTableNode", "cross_ref": "flow_rag.adapters.file_loader.utils.load_files_generic", "kind": "Gdef"}}, "path": "flow_rag/adapters/file_loader/__init__.py"}