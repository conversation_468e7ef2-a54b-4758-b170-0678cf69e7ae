{"data_mtime": 1750709599, "dep_lines": [8, 9, 4, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["flow_rag.domain.ports.file_loader", "flow_rag.adapters.file_loader.utils", "os", "logging", "typing", "builtins", "_frozen_importlib", "abc", "enum", "flow_rag.domain", "flow_rag.domain.ports", "types", "typing_extensions"], "hash": "ed44afce55d7d8bcabeb18b0bd13ae960212690a", "id": "flow_rag.adapters.file_loader.loader", "ignore_all": false, "interface_hash": "74b1e1cf3071bdad7f227dd09ec593290f94f6af", "mtime": 1750707764, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "flow_rag/adapters/file_loader/loader.py", "plugin_data": null, "size": 2227, "suppressed": [], "version_id": "1.16.1"}