{"data_mtime": 1750705745, "dep_lines": [21, 22, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30, 30], "dependencies": ["flow_rag.adapters.file_loader.loader", "flow_rag.adapters.file_loader.utils", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "08abc10b1ad625babd3524e0b7cf431a2c098fe6", "id": "flow_rag.adapters.file_loader", "ignore_all": false, "interface_hash": "354c563522dced8ee19e900eff795ee47af8b07d", "mtime": 1749496089, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "flow_rag/adapters/file_loader/__init__.py", "plugin_data": null, "size": 794, "suppressed": [], "version_id": "1.16.1"}