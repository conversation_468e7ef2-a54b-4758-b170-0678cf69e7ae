{"data_mtime": 1750703629, "dep_lines": [3, 5, 62, 1, 2, 3, 4, 6, 7, 8, 9, 23, 24, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 10, 20, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["unittest.result", "collections.abc", "unittest._log", "logging", "sys", "unittest", "_typeshed", "contextlib", "re", "types", "typing", "typing_extensions", "warnings", "builtins", "_frozen_importlib", "abc"], "hash": "1dd0bf52639d36b2cb2432aae3719c743a3183e1", "id": "unittest.case", "ignore_all": true, "interface_hash": "6d4ac912c35e43d15c247c47de9a5601decc2fb1", "mtime": 1750435055, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/mypy/typeshed/stdlib/unittest/case.pyi", "plugin_data": null, "size": 14929, "suppressed": [], "version_id": "1.16.1"}