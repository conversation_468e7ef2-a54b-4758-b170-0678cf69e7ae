{"data_mtime": 1750703629, "dep_lines": [6, 7, 9, 15, 3, 6, 13, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 25, 10, 20, 25, 5, 30, 30, 30, 30, 30], "dependencies": ["jinja2.nodes", "jinja2.exceptions", "jinja2.lexer", "jinja2.environment", "typing", "jinja2", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "jinja2.ext", "types"], "hash": "a2dec10dae42d2bf87366a5cb8d1e2baa3ad1d1a", "id": "jinja2.parser", "ignore_all": true, "interface_hash": "c43d172db7dc058167d412e14b1d16d1e47e5aed", "mtime": 1747857486, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/jinja2/parser.py", "plugin_data": null, "size": 40383, "suppressed": [], "version_id": "1.16.1"}