{"data_mtime": 1750703629, "dep_lines": [9, 11, 12, 14, 18, 20, 21, 22, 1, 3, 4, 5, 6, 7, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 25, 25, 25, 25, 5, 10, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["werkzeug.exceptions", "flask.typing", "flask.globals", "flask.signals", "_typeshed.wsgi", "flask.app", "flask.sessions", "flask.wrappers", "__future__", "<PERSON><PERSON><PERSON>", "sys", "typing", "functools", "types", "flask", "builtins", "_contextvars", "_frozen_importlib", "abc", "flask.json", "flask.json.provider", "flask.sansio", "flask.sansio.app", "flask.sansio.scaffold", "werkzeug", "werkzeug.datastructures", "werkzeug.datastructures.mixins", "werkzeug.datastructures.structures", "werkzeug.routing", "werkzeug.routing.map", "werkzeug.routing.rules", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.sansio.response", "werkzeug.wrappers", "werkzeug.wrappers.request"], "hash": "ecfba4579a7cf2fca5912e6a6b6526bc1c9aaadc", "id": "flask.ctx", "ignore_all": true, "interface_hash": "717bd6902f438b6d7841e3d967787a8d5d919fa7", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/flask/ctx.py", "plugin_data": null, "size": 15120, "suppressed": [], "version_id": "1.16.1"}