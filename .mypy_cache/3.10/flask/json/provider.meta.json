{"data_mtime": 1750703629, "dep_lines": [14, 16, 11, 1, 3, 4, 5, 6, 7, 8, 9, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 5, 5, 10, 10, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["werkzeug.sansio.response", "flask.sansio.app", "werkzeug.http", "__future__", "dataclasses", "decimal", "json", "typing", "uuid", "weakref", "datetime", "builtins", "_frozen_importlib", "_weakref", "abc", "flask.sansio", "flask.sansio.scaffold", "werkzeug", "werkzeug.sansio"], "hash": "78e222a34c3654798e2bc1bda7a6f26fff362739", "id": "flask.json.provider", "ignore_all": true, "interface_hash": "20bd83c8a16d1ae2484a46b38382217a3b993bd8", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/flask/json/provider.py", "plugin_data": null, "size": 7672, "suppressed": [], "version_id": "1.16.1"}