{"data_mtime": 1750703629, "dep_lines": [3, 13, 15, 17, 18, 19, 1, 3, 4, 5, 6, 7, 8, 9, 11, 17, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 5, 5, 5, 20, 10, 10, 10, 10, 5, 5, 5, 20, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["importlib.util", "werkzeug.exceptions", "werkzeug.utils", "flask.typing", "flask.helpers", "flask.templating", "__future__", "importlib", "os", "pathlib", "sys", "typing", "collections", "functools", "jinja2", "flask", "click", "builtins", "_frozen_importlib", "_typeshed", "_typeshed.wsgi", "abc", "click.core", "jinja2.loaders", "werkzeug", "werkzeug.datastructures", "werkzeug.datastructures.headers", "werkzeug.sansio", "werkzeug.sansio.response"], "hash": "ebfc1f2ac72875c2a52c46be8ac7104f4f8abecc", "id": "flask.sansio.scaffold", "ignore_all": true, "interface_hash": "d5086500977fa6789842b811bd787110b1f818a6", "mtime": 1747857490, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/flask/sansio/scaffold.py", "plugin_data": null, "size": 30387, "suppressed": [], "version_id": "1.16.1"}