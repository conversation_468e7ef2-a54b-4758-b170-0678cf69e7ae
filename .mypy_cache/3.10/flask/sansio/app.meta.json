{"data_mtime": **********, "dep_lines": [16, 26, 31, 41, 10, 13, 17, 20, 21, 23, 24, 28, 29, 37, 39, 1, 3, 4, 5, 6, 7, 8, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 5, 5, 5, 10, 5, 5, 5, 5, 5, 25, 25, 5, 10, 10, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["werkzeug.sansio.response", "flask.json.provider", "flask.sansio.scaffold", "flask.sansio.blueprints", "werkzeug.exceptions", "werkzeug.routing", "werkzeug.utils", "flask.typing", "flask.config", "flask.ctx", "flask.helpers", "flask.logging", "flask.templating", "werkzeug.wrappers", "flask.testing", "__future__", "logging", "os", "sys", "typing", "datetime", "itertools", "flask", "builtins", "_frozen_importlib", "_typeshed", "_typeshed.wsgi", "abc", "click", "click.testing", "flask.json", "jinja2", "jinja2.environment", "jinja2.loaders", "posixpath", "types", "typing_extensions", "werkzeug", "werkzeug.datastructures", "werkzeug.datastructures.headers", "werkzeug.routing.converters", "werkzeug.routing.exceptions", "werkzeug.routing.map", "werkzeug.routing.rules", "werkzeug.sansio", "werkzeug.test", "werkzeug.wrappers.response"], "hash": "35f4731e85799f24681259519688cf3593e3902a", "id": "flask.sansio.app", "ignore_all": true, "interface_hash": "d3abc8248b0bf507ca25ee0c73dfe9620a62d073", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/flask/sansio/app.py", "plugin_data": null, "size": 38116, "suppressed": [], "version_id": "1.16.1"}