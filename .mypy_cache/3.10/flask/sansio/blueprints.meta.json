{"data_mtime": 1750703629, "dep_lines": [9, 15, 8, 1, 3, 4, 5, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 10, 5, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["flask.sansio.scaffold", "flask.sansio.app", "flask.typing", "__future__", "os", "typing", "collections", "functools", "flask", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "_typeshed.wsgi", "abc", "click", "click.core", "werkzeug", "werkzeug.datastructures", "werkzeug.datastructures.headers", "werkzeug.sansio", "werkzeug.sansio.response"], "hash": "8fa1083cf91fa55b917afa326dbf87f437e15cc2", "id": "flask.sansio.blueprints", "ignore_all": true, "interface_hash": "08b8dc6d4aa8230438c803361e8ba7538d23ebc3", "mtime": 1747857490, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/flask/sansio/blueprints.py", "plugin_data": null, "size": 24637, "suppressed": [], "version_id": "1.16.1"}