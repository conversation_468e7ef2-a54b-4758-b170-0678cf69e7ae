{"data_mtime": 1750703629, "dep_lines": [4, 5, 18, 20, 21, 23, 24, 30, 34, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 19, 28, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 818], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 25, 25, 5, 10, 20, 20, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["collections.abc", "importlib.metadata", "click.core", "werkzeug.serving", "werkzeug.utils", "flask.globals", "flask.helpers", "_typeshed.wsgi", "flask.app", "__future__", "ast", "collections", "importlib", "inspect", "os", "platform", "re", "sys", "traceback", "typing", "functools", "operator", "types", "click", "werkzeug", "ssl", "builtins", "_frozen_importlib", "_ssl", "_typeshed", "abc", "click.decorators", "click.exceptions", "click.types", "enum", "flask.sansio", "flask.sansio.app", "flask.sansio.scaffold", "typing_extensions"], "hash": "9a94e72e371871c7ff31d3e3f100478281038e75", "id": "flask.cli", "ignore_all": true, "interface_hash": "ca86d2c1b4ede2ac863d5a9566c4655060a0452f", "mtime": 1747857490, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/flask/cli.py", "plugin_data": null, "size": 37184, "suppressed": ["cryptography"], "version_id": "1.16.1"}