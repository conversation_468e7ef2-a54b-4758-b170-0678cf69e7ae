{"data_mtime": **********, "dep_lines": [5, 7, 10, 11, 12, 15, 208, 1, 3, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 25, 20, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["werkzeug.exceptions", "werkzeug.wrappers", "flask.json", "flask.globals", "flask.helpers", "werkzeug.routing", "flask.debughelpers", "__future__", "typing", "flask", "builtins", "_frozen_importlib", "abc", "json", "types", "werkzeug", "werkzeug.routing.rules", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.sansio.response", "werkzeug.wrappers.request", "werkzeug.wrappers.response"], "hash": "22974913f851754da27bf4832268ecd3c849e81a", "id": "flask.wrappers", "ignore_all": true, "interface_hash": "39eec84b357d2f7a96fc5d4e1bbb83ea941aa3cb", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/flask/wrappers.py", "plugin_data": null, "size": 9406, "suppressed": [], "version_id": "1.16.1"}