{"data_mtime": 1750703629, "dep_lines": [44, 45, 3, 12, 15, 17, 20, 25, 26, 27, 29, 30, 31, 33, 40, 46, 48, 53, 54, 58, 61, 502, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 29, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 995], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 25, 20, 20, 5, 20, 10, 10, 10, 10, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["flask.sansio.app", "flask.sansio.scaffold", "collections.abc", "urllib.parse", "werkzeug.datastructures", "werkzeug.exceptions", "werkzeug.routing", "werkzeug.serving", "werkzeug.wrappers", "werkzeug.wsgi", "flask.cli", "flask.typing", "flask.ctx", "flask.globals", "flask.helpers", "flask.sessions", "flask.signals", "flask.templating", "flask.wrappers", "_typeshed.wsgi", "flask.testing", "flask.debughelpers", "__future__", "collections", "os", "sys", "typing", "weakref", "datetime", "inspect", "itertools", "types", "click", "flask", "builtins", "_frozen_importlib", "_ssl", "_typeshed", "abc", "blinker", "blinker.base", "click.core", "click.termui", "click.testing", "enum", "flask.config", "flask.json", "flask.json.provider", "flask.sansio", "http", "http.server", "jinja2", "jinja2.environment", "socketserver", "ssl", "typing_extensions", "werkzeug", "werkzeug.datastructures.headers", "werkzeug.datastructures.mixins", "werkzeug.datastructures.structures", "werkzeug.routing.map", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.sansio.response", "werkzeug.test", "werkzeug.wrappers.request", "werkzeug.wrappers.response"], "hash": "e05c8f80af25fdb3019bb724aa602e86c05117af", "id": "flask.app", "ignore_all": true, "interface_hash": "a7f4141067dcff7f8b4e32f6b13504aacdea2527", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/flask/app.py", "plugin_data": null, "size": 61744, "suppressed": ["asgiref.sync"], "version_id": "1.16.1"}