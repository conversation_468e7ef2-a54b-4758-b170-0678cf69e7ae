{"data_mtime": 1750703629, "dep_lines": [3, 9, 11, 12, 15, 17, 18, 21, 24, 1, 3, 4, 5, 7, 8, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 25, 25, 5, 20, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["importlib.metadata", "urllib.parse", "werkzeug.test", "click.testing", "werkzeug.wrappers", "flask.cli", "flask.sessions", "_typeshed.wsgi", "flask.app", "__future__", "importlib", "typing", "contextlib", "copy", "types", "werkzeug", "builtins", "_frozen_importlib", "_typeshed", "abc", "click", "flask.config", "flask.json", "flask.json.provider", "flask.sansio", "flask.sansio.app", "flask.sansio.scaffold", "json", "urllib", "werkzeug.datastructures", "werkzeug.datastructures.auth", "werkzeug.datastructures.headers", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.sansio.response", "werkzeug.wrappers.request", "werkzeug.wrappers.response"], "hash": "02568bdc2651267016d7db5d2ee55361caef6d83", "id": "flask.testing", "ignore_all": true, "interface_hash": "77df3364b9502020868b77b4d86773d9daa067e1", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/flask/testing.py", "plugin_data": null, "size": 10136, "suppressed": [], "version_id": "1.16.1"}