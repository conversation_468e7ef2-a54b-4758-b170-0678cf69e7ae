{"data_mtime": 1750703634, "dep_lines": [6, 7, 8, 9, 20, 1, 3, 4, 23, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 25, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["httpx._client", "httpx._config", "httpx._models", "httpx._types", "httpx._urls", "__future__", "typing", "contextlib", "ssl", "builtins", "_frozen_importlib", "_ssl", "abc", "http", "http.cookiejar", "httpx._auth"], "hash": "428c33f18aab47e41176149ed14d94bd8ecdc7b7", "id": "httpx._api", "ignore_all": true, "interface_hash": "fedabcc32b6cc8b2b4641169099496185f44427a", "mtime": 1747857490, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/httpx/_api.py", "plugin_data": null, "size": 11743, "suppressed": [], "version_id": "1.16.1"}