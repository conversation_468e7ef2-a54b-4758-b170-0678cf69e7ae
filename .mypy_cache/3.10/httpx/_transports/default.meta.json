{"data_mtime": 1750703634, "dep_lines": [58, 38, 39, 55, 56, 57, 27, 29, 30, 31, 34, 36, 150, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 189], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 25, 25, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["httpx._transports.base", "httpx._config", "httpx._exceptions", "httpx._models", "httpx._types", "httpx._urls", "__future__", "contextlib", "typing", "types", "ssl", "httpx", "httpcore", "builtins", "_frozen_importlib", "_ssl", "abc", "httpcore._async", "httpcore._async.connection_pool", "httpcore._async.http_proxy", "httpcore._async.interfaces", "httpcore._async.socks_proxy", "httpcore._backends", "httpcore._backends.base", "httpcore._models", "httpcore._sync", "httpcore._sync.connection_pool", "httpcore._sync.http_proxy", "httpcore._sync.interfaces", "httpcore._sync.socks_proxy", "typing_extensions"], "hash": "79fd270cb3f27adcc29817efed9b57bd125b3b1d", "id": "httpx._transports.default", "ignore_all": true, "interface_hash": "eb1dd5b310a30017c774f996f8e67dbfe8441f41", "mtime": 1747857490, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/httpx/_transports/default.py", "plugin_data": null, "size": 13983, "suppressed": ["<PERSON><PERSON>"], "version_id": "1.16.1"}