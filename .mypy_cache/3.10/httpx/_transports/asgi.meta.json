{"data_mtime": 1750703628, "dep_lines": [7, 5, 6, 1, 3, 10, 12, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 25, 25, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["httpx._transports.base", "httpx._models", "httpx._types", "__future__", "typing", "asyncio", "trio", "builtins", "_frozen_importlib", "abc", "asyncio.locks", "asyncio.mixins", "trio._sync", "typing_extensions"], "hash": "2f92761e50ff6888bb37e9e477b66c6d79a014c1", "id": "httpx._transports.asgi", "ignore_all": true, "interface_hash": "73fe7dfe53abbf07de1d31d09d293a836b98b59d", "mtime": 1747857490, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/httpx/_transports/asgi.py", "plugin_data": null, "size": 5501, "suppressed": [], "version_id": "1.16.1"}