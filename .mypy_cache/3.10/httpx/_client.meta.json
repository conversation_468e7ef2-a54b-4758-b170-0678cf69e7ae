{"data_mtime": 1750703634, "dep_lines": [31, 32, 12, 13, 14, 22, 23, 29, 30, 33, 48, 49, 1, 3, 4, 5, 6, 7, 8, 9, 10, 52, 1, 1, 1, 1, 1, 1, 1, 1, 1, 678], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["httpx._transports.base", "httpx._transports.default", "httpx.__version__", "httpx._auth", "httpx._config", "httpx._decoders", "httpx._exceptions", "httpx._models", "httpx._status_codes", "httpx._types", "httpx._urls", "httpx._utils", "__future__", "datetime", "enum", "logging", "time", "typing", "warnings", "contextlib", "types", "ssl", "builtins", "_collections_abc", "_frozen_importlib", "_ssl", "_typeshed", "abc", "http", "http.cookiejar", "httpx._transports"], "hash": "ffd9e6a06cdab64c71aef8ef7be33eef7ba5db67", "id": "httpx._client", "ignore_all": true, "interface_hash": "f94ff53687afbf8d983b7e686ec2d15e82e4eec6", "mtime": 1747857490, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/httpx/_client.py", "plugin_data": null, "size": 65714, "suppressed": ["h2"], "version_id": "1.16.1"}