{"data_mtime": 1750703634, "dep_lines": [17, 18, 19, 20, 1, 3, 4, 5, 6, 8, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 9, 10, 11, 12, 13, 14, 15, 9, 11], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 10, 10, 10, 10, 10, 20, 20], "dependencies": ["httpx._client", "httpx._exceptions", "httpx._models", "httpx._status_codes", "__future__", "functools", "json", "sys", "typing", "click", "httpcore", "builtins", "_frozen_importlib", "_typeshed", "abc", "click.core", "click.decorators", "click.types", "httpcore._models", "typing_extensions"], "hash": "f1e376e6e69a9013fe57cced3181ff8b9bc1a95a", "id": "httpx._main", "ignore_all": true, "interface_hash": "cb53ebdac1eb9b0fd38a03723dd800ba3af0db4f", "mtime": 1747857490, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/httpx/_main.py", "plugin_data": null, "size": 15626, "suppressed": ["pygments.lexers", "pygments.util", "rich.console", "rich.markup", "rich.progress", "rich.syntax", "rich.table", "pygments", "rich"], "version_id": "1.16.1"}