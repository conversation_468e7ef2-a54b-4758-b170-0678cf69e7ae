{"data_mtime": 1750703632, "dep_lines": [27, 4, 18, 19, 1, 2, 3, 5, 17, 44, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 10, 10, 10, 5, 10, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["numpy.polynomial._polytypes", "collections.abc", "numpy.typing", "numpy._typing", "abc", "decimal", "numbers", "typing", "numpy", "typing_extensions", "builtins", "_frozen_importlib", "numpy._typing._array_like", "numpy._typing._nbit_base", "numpy._typing._nested_sequence", "types"], "hash": "4410e11fa70cab49749fe6d5dc73f8880802a866", "id": "numpy.polynomial._polybase", "ignore_all": true, "interface_hash": "f5a9f1041c1a1fe48bf230fbd844bbd457c2563b", "mtime": 1744395152, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/numpy/polynomial/_polybase.pyi", "plugin_data": null, "size": 8534, "suppressed": [], "version_id": "1.16.1"}