{"data_mtime": 1750703632, "dep_lines": [65, 66, 76, 75, 1, 2, 9, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["numpy._core._type_aliases", "numpy._core.multiarray", "numpy._typing._extended_precision", "numpy._typing", "builtins", "typing", "numpy", "_frozen_importlib", "abc", "datetime", "numpy._typing._dtype_like", "numpy._typing._nbit_base"], "hash": "9b1d5768611ecec2b4b3a32145c68bce2e5f909f", "id": "numpy._core.numerictypes", "ignore_all": true, "interface_hash": "bee360c5d6d907b546218001a50c3510ead25317", "mtime": 1744395152, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": false, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/.pyenv/versions/3.12.5/lib/python3.12/site-packages/numpy/_core/numerictypes.pyi", "plugin_data": null, "size": 3533, "suppressed": [], "version_id": "1.16.1"}