#!/usr/bin/env python3
"""
Complete FlowRAG Example

Demonstrates all main features of FlowRAG v2.0.
"""

import os
import flow_rag

def main():
    # Configure codebase path - change this to your actual codebase
    codebase_path = "/path/to/your/codebase"

    if not os.path.exists(codebase_path):
        print("❌ Please update codebase_path to a valid directory")
        return

    print(f"📁 Analyzing: {os.path.basename(codebase_path)}")

    # Initialize codebase
    flow_rag.initialize(codebase_path)
    print("✅ Indexing complete!")

    # Check connection to FlowAPI
    if not flow_rag.check_connection():
        print("❌ FlowAPI connection failed. Please check your credentials and network connection.")
        return

    # 1. Basic query
    print("\n🔍 Basic Query:")
    result1 = flow_rag.query("What does this project do?", codebase_path)
    print(f"Answer: {result1}...")

    # 2. Custom format with user_prompt
    print("\n🔍 JSON Format Query:")
    result2 = flow_rag.query(
        "List the main classes in this project",
        codebase_path,
        output_format="Return as JSON array with class names and descriptions"
    )
    print(f"JSON Result: {result2}...")

    # 3. Context-only (no AI processing)
    print("\n🔍 Context-Only Query:")
    context = flow_rag.get_context("error handling functions", codebase_path)
    print(f"Context found: {len(context)} characters")

    # 4. Query with context returned
    print("\n🔍 Query with Context:")
    result_with_context = flow_rag.query(
        "How does authentication work?",
        codebase_path,
        return_context=True
    )

    if isinstance(result_with_context, dict):
        print(f"Answer: {result_with_context.get('answer', result_with_context)[:100]}...")
        print(f"Context length: {len(result_with_context.get('context', ''))}")

    # Save all results
    with open("complete_example_output.txt", "w", encoding="utf-8") as f:
        f.write("FlowRAG v2.0 Complete Example Results\n")
        f.write("=" * 45 + "\n\n")
        f.write(f"1. Basic Query:\n{result1}\n\n")
        f.write(f"2. JSON Format Query:\n{result2}\n\n")
        f.write(f"3. Context Only:\n{context}\n\n")
        if isinstance(result_with_context, dict):
            f.write(f"4. Query with Context:\n{result_with_context.get('answer', result_with_context)}\n")

    print("✅ Results saved to 'complete_example_output.txt'")

if __name__ == "__main__":
    main()


