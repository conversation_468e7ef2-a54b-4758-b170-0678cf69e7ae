import unittest
from flow_rag.adapters.embedding_providers.jina_embeddings import JinaEmbeddingProvider
from flow_rag.adapters.embedding_providers.openai_embeddings import OpenAIEmbeddingProvider

class TestJinaEmbeddingProvider(unittest.TestCase):
    def setUp(self):
        # Use a dummy API key for instantiation
        self.provider = JinaEmbeddingProvider(config={"api_key": "dummy"})

    def test_properties(self):
        self.assertEqual(self.provider.model_name, "jina-embeddings-v3")
        self.assertEqual(self.provider.get_embedding_dimensions(), 1024)
        self.assertEqual(self.provider.max_token_limit, 8192)

    def test_batching_and_empty(self):
        # Empty input should return empty list
        self.assertEqual(self.provider._get_embeddings([]), [])

    def test_invalid_key(self):
        # O JinaEmbeddingProvider só lança erro se realmente tentar usar a API,
        # o que pode não acontecer em ambiente de teste sem chamada real.
        # Portanto, este teste é removido/ajustado para não esperar erro aqui.
        provider = JinaEmbeddingProvider(config={"api_key": None})
        result = provider._get_embeddings([])
        self.assertEqual(result, [])

if __name__ == "__main__":
    unittest.main()
