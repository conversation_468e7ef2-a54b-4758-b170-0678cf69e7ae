from pathlib import Path
import pytest

from flow_rag.domain.models import LanguageEnum
from flow_rag.adapters.code_parsers.factory import CodeParserFactory

@pytest.fixture
def parser():
    # Usar python como linguagem base para criar o parser
    return CodeParserFactory.create_parser(LanguageEnum.PYTHON)


@pytest.fixture
def temp_dir(tmp_path):
    # tmp_path é um pathlib.Path único por teste
    yield tmp_path
    # Limpeza automática pelo pytest

def create_test_file(tmpdir, filename, content):
    file_path = tmpdir / filename
    file_path.parent.mkdir(parents=True, exist_ok=True)
    file_path.write_text(content)
    return str(file_path)

def test_get_language_from_extension(parser):
    assert parser.get_language_from_extension(".py") == LanguageEnum.PYTHON
    assert parser.get_language_from_extension(".java") == LanguageEnum.JAVA
    assert parser.get_language_from_extension(".js") == LanguageEnum.JAVASCRIPT
    assert parser.get_language_from_extension(".txt") == LanguageEnum.TEXT


def test_load_files(parser, temp_dir):
    # Cria arquivos de teste
    py_file = create_test_file(temp_dir, "test.py", "def hello(): pass")
    java_file = create_test_file(temp_dir, "test.java", "class Test { void hello() {} }")
    js_file = create_test_file(temp_dir, "test.js", "function hello() {}")
    create_test_file(temp_dir, "test.txt", "Not a code file")  # Deve ser ignorado

    # Cria diretório blacklisted
    blacklist_dir = Path(temp_dir) / "__pycache__"
    blacklist_dir.mkdir()
    create_test_file(blacklist_dir, "ignored.py", "# Should be ignored")

    # Carrega arquivos
    file_list = parser.load_files(str(temp_dir))

    # Verifica resultados
    file_paths = [path for path, _ in file_list]
    assert len(file_list) == 3
    assert py_file in file_paths
    assert java_file in file_paths
    assert js_file in file_paths


def test_should_ignore_path(parser, temp_dir):
    # Cria .gitignore
    gitignore_content = """
# Python
__pycache__/
*.py[cod]
*$py.class
.env
venv/

# Node
node_modules/
"""
    create_test_file(temp_dir, ".gitignore", gitignore_content)

    # Carrega padrões do gitignore
    parser.gitignore_patterns = parser._load_gitignore_patterns(str(temp_dir))

    # Testa ignorados
    assert parser.should_ignore_path(str(Path(temp_dir) / "__pycache__"))
    assert parser.should_ignore_path(str(Path(temp_dir) / "test.pyc"))
    assert parser.should_ignore_path(str(Path(temp_dir) / "venv"))
    assert parser.should_ignore_path(str(Path(temp_dir) / "node_modules"))

    # Testa não ignorados
    assert not parser.should_ignore_path(str(Path(temp_dir) / "src"))
    assert not parser.should_ignore_path(str(Path(temp_dir) / "test.py"))


if __name__ == "__main__":
    pytest.main()