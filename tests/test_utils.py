import unittest
import tempfile
import os
from unittest.mock import MagicMock

from flow_rag.utils.reference_optimizer import OptimizedReferenceDiscovery, Symbol
from flow_rag.utils.smart_chunking import SmartChunker, ChunkingConfig
from flow_rag.utils.simple_reference_discovery import create_simple_reference_discovery, SimpleReferenceDiscovery
from flow_rag.domain.models import Method, Class
from flow_rag.domain.ports.file_loader import LanguageEnum


class TestReferenceOptimizer(unittest.TestCase):
    """Test cases for OptimizedReferenceDiscovery."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.logger = MagicMock()
        
        # Create test methods and classes
        self.test_methods = [
            Method(
                name="getUserName",
                file_path="user.py",
                source_code="def getUserName(): return 'John'",
                class_name="User"
            ),
            Method(
                name="validateLogin",
                file_path="auth.py", 
                source_code="def validateLogin(user): return getUserName() == user",
                class_name="Auth"
            )
        ]
        
        self.test_classes = [
            Class(
                name="User",
                file_path="user.py",
                source_code="class User: pass"
            ),
            Class(
                name="Auth",
                file_path="auth.py",
                source_code="class Auth: pass"
            )
        ]
    
    def test_optimized_reference_discovery_creation(self):
        """Test creation of OptimizedReferenceDiscovery."""
        discovery = OptimizedReferenceDiscovery(self.logger)

        self.assertIsNotNone(discovery)
        self.assertEqual(len(discovery.symbol_index), 0)
    
    def test_symbol_creation(self):
        """Test Symbol creation and properties."""
        symbol = Symbol("getUserName", "method", "user.py", 10)

        self.assertEqual(symbol.name, "getUserName")
        self.assertEqual(symbol.file_path, "user.py")
        self.assertEqual(symbol.line_number, 10)
    
    def test_find_references_parallel(self):
        """Test parallel reference finding."""
        discovery = OptimizedReferenceDiscovery(self.logger)

        # Build symbol index with test data
        files_and_symbols = [
            ("user.py", [Symbol("User", "class", "user.py", 1)]),
            ("auth.py", [Symbol("authenticate", "method", "auth.py", 5)])
        ]
        discovery.build_symbol_index(files_and_symbols)

        references = discovery.find_references_parallel(max_workers=2)

        self.assertIsInstance(references, dict)
        # Should find getUserName reference in validateLogin
        if "getUserName" in references:
            self.assertGreater(len(references["getUserName"]), 0)


class TestSmartChunking(unittest.TestCase):
    """Test cases for SmartChunker."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = ChunkingConfig(
            max_chunk_size=100,
            overlap_size=20
        )
        self.chunker = SmartChunker(self.config)
    
    def test_chunking_config(self):
        """Test ChunkingConfig creation."""
        self.assertEqual(self.config.max_chunk_size, 100)
        self.assertEqual(self.config.overlap_size, 20)
    
    def test_smart_chunker_creation(self):
        """Test SmartChunker creation."""
        self.assertIsNotNone(self.chunker)
        self.assertEqual(self.chunker.config, self.config)
    
    def test_chunk_file_simple(self):
        """Test simple file chunking."""
        content = "This is a simple text that needs to be chunked into smaller pieces for processing."

        chunks = self.chunker.chunk_file("test.txt", content)

        self.assertIsInstance(chunks, list)
        self.assertGreater(len(chunks), 0)

        # Each chunk should have proper structure
        for chunk in chunks:
            self.assertIsInstance(chunk.content, str)
            self.assertIsInstance(chunk.start_line, int)
            self.assertIsInstance(chunk.end_line, int)
    
    def test_chunk_file_short(self):
        """Test chunking of short file."""
        short_content = "Short text"

        chunks = self.chunker.chunk_file("test.txt", short_content)

        # Short content should return single chunk
        self.assertEqual(len(chunks), 1)
        self.assertEqual(chunks[0].content, short_content)
    
    def test_chunk_code_file(self):
        """Test code file chunking."""
        code = """def function1():
    return "Hello"

def function2():
    return "World"

class MyClass:
    def method1(self):
        pass

    def method2(self):
        pass"""

        chunks = self.chunker.chunk_file("test.py", code)

        self.assertIsInstance(chunks, list)
        self.assertGreater(len(chunks), 0)

        # Should preserve code structure
        for chunk in chunks:
            self.assertIsInstance(chunk.content, str)
            self.assertGreater(len(chunk.content), 0)


class TestSimpleReferenceDiscovery(unittest.TestCase):
    """Test cases for SimpleReferenceDiscovery."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.logger = MagicMock()
        
        # Create test methods and classes
        self.test_methods = [
            Method(
                name="getUserName",
                file_path="user.py",
                source_code="def getUserName(): return 'John'",
                class_name="User"
            ),
            Method(
                name="validateLogin", 
                file_path="auth.py",
                source_code="def validateLogin(user): return getUserName() == user",
                class_name="Auth"
            )
        ]
        
        self.test_classes = [
            Class(
                name="User",
                file_path="user.py",
                source_code="class User: pass"
            )
        ]
    
    def test_create_simple_reference_discovery(self):
        """Test factory function for SimpleReferenceDiscovery."""
        discovery = create_simple_reference_discovery(
            self.test_classes,
            self.test_methods,
            self.logger
        )
        
        self.assertIsInstance(discovery, SimpleReferenceDiscovery)
        self.assertIsNotNone(discovery.symbol_files)
    
    def test_simple_reference_discovery_creation(self):
        """Test direct creation of SimpleReferenceDiscovery."""
        discovery = SimpleReferenceDiscovery(self.logger)

        # Build symbol index
        files_and_symbols = [
            ("user.py", [self.test_classes[0]]),
            ("auth.py", [self.test_methods[0]])
        ]
        discovery.build_symbol_index(files_and_symbols)

        self.assertGreater(len(discovery.symbol_files), 0)
    
    def test_find_references_simple(self):
        """Test simple reference finding."""
        discovery = create_simple_reference_discovery(
            self.test_classes,
            self.test_methods,
            self.logger
        )
        
        # Create temporary test files
        temp_dir = tempfile.mkdtemp()
        
        try:
            # Create test file with references
            test_file = os.path.join(temp_dir, "test.py")
            with open(test_file, 'w') as f:
                f.write("result = getUserName()\nuser = User()")
            
            file_list = [(test_file, LanguageEnum.PYTHON)]
            
            # Find references
            references = discovery.find_references_simple(file_list)
            
            self.assertIsInstance(references, dict)
            self.assertIn('class', references)
            self.assertIn('method', references)
            
            # Should find references
            method_refs = references.get('method', {})
            class_refs = references.get('class', {})
            
            # Check if getUserName reference was found
            if 'getUserName' in method_refs:
                self.assertGreater(len(method_refs['getUserName']), 0)
            
            # Check if User reference was found
            if 'User' in class_refs:
                self.assertGreater(len(class_refs['User']), 0)
        
        finally:
            # Cleanup
            import shutil
            try:
                shutil.rmtree(temp_dir, ignore_errors=True)
            except:
                pass
    
    def test_find_references_empty_files(self):
        """Test reference finding with empty file list."""
        discovery = create_simple_reference_discovery(
            self.test_classes,
            self.test_methods,
            self.logger
        )
        
        references = discovery.find_references_simple([])
        
        self.assertIsInstance(references, dict)
        self.assertEqual(references.get('class', {}), {})
        self.assertEqual(references.get('method', {}), {})
    
    def test_find_references_no_symbols(self):
        """Test reference finding with no symbols."""
        discovery = SimpleReferenceDiscovery(self.logger)
        
        # Create temporary test file
        temp_dir = tempfile.mkdtemp()
        
        try:
            test_file = os.path.join(temp_dir, "test.py")
            with open(test_file, 'w') as f:
                f.write("print('hello world')")
            
            file_list = [(test_file, LanguageEnum.PYTHON)]
            
            references = discovery.find_references_simple(file_list)
            
            self.assertIsInstance(references, dict)
            self.assertEqual(references.get('class', {}), {})
            self.assertEqual(references.get('method', {}), {})
        
        finally:
            # Cleanup
            import shutil
            try:
                shutil.rmtree(temp_dir, ignore_errors=True)
            except:
                pass


if __name__ == "__main__":
    unittest.main()
