# FlowRAG Library - Usage Guide

This library can be used in two ways:

1. Using the web interface or command-line to index and search code
2. Using as a library in your own code

## Usage with Chat Interface or Command-Line Interface

### Setup

#### 1. Clone the repository and setup the environment:

```bash
# Clone the repository
<NAME_EMAIL>:ciandt_it/pyflow_rag.git
cd pyflow_rag

# Setup the environment:
# all the configuration will be done and checked here, so is recommended just run:
#################
./setup_env.sh
#################
```

Or you can set the configuration manually:

```bash
#1. Configure your environment variables from the .env.example file
cp .env.example .env

#2. Install dependencies
pip install -r requirements.txt
```

#### 2. Set up the required API keys in your environment:

```bash
FLOW_API_CLIENT_ID="your-client-id" # Required
FLOW_API_CLIENT_SECRET="your-client-secret" # Required
FLOW_API_APP_TO_ACCESS="your-app-to-access" # Required
FLOW_API_TENANT="your-flow-tenant" # Required

FLOW_API_LOG_FILE_PATH=/path/to/flow_api.log # Optional
FLOW_API_CONSOLE_LOG_LEVEL=OFF # Optional
FLOW_API_FILE_LOG_LEVEL=ALL  # Optional

STORE_BASE_PATH="path/to/your/store" # Optional
LOGS_BASE_PATH="path/to/your/logs" # Optional
```

### Using the Chat and Command-Line Interface

The `index.sh` script provides a streamlined interface for using the library:

#### Indexing and Starting the Web Interface

```bash
./index.sh /path/to/your/codebase
```

If you don't provide a codebase path, the script will prompt you to enter one.

This will:

1. Parse the codebase using Tree-sitter and specialized parsers
2. Find references between code entities
3. Index special files (README, configs, etc.) with intelligent chunking for large files
4. Store the indexed data in SQLite database in the `rag_data` or your configured directory
5. Automatically start the web server at http://localhost:5001 for chat interface

You can then interact with your codebase through the web interface.

#### Starting Web Server Only

If you've already indexed your codebase and just want to start the web server:

```bash
python -m flow_rag.infrastructure.web.entrypoints.run_web /path/to/your/codebase
```

.
.
.

## Using as a Library

### Setup

1. Add the repository in your requirements.txt file or install it via pip:

```bash
pip install git+https://bitbucket.org/ciandt_it/pyflow_rag.git
```

2. Add the required API keys in your environment variables:

```bash
FLOW_API_CLIENT_ID="your-client-id" # Required
FLOW_API_CLIENT_SECRET="your-client-secret" # Required
FLOW_API_APP_TO_ACCESS="your-app-to-access" # Required
FLOW_API_TENANT="your-flow-tenant" # Required

FLOW_API_LOG_FILE_PATH=/path/to/flow_api.log # Optional
FLOW_API_CONSOLE_LOG_LEVEL=OFF # Optional
FLOW_API_FILE_LOG_LEVEL=ALL  # Optional

STORE_BASE_PATH="path/to/your/store" # Optional
LOGS_BASE_PATH="path/to/your/logs" # Optional
```

### Basic Usage

#### 1. Initialize and Query

```python
import flow_rag

# Initialize the codebase (indexes if needed)
flow_rag.initialize("/path/to/your/codebase")

# Optional: Start FlowAPI connection to be ready before queries
if flow_rag.check_connection():
    print("✅ FlowAPI connection ready")

# Query with AI processing
response = flow_rag.query(
    "How does the authentication flow work?",
    "/path/to/your/codebase"
)
print(response)
```

#### 2. Context-Only Mode (No AI Processing)

```python
import flow_rag

# Initialize the codebase
flow_rag.initialize("/path/to/your/codebase")

# Get only relevant context without AI processing
context = flow_rag.get_context(
    "authentication functions",
    "/path/to/your/codebase"
)
print(context)
```

#### 3. Custom Output Formats

```python
import flow_rag

# Initialize once
flow_rag.initialize("/path/to/your/codebase")

# Get structured response using user_prompt
json_response = flow_rag.query(
    "List the main classes in this project",
    "/path/to/your/codebase",
    user_prompt="Return as JSON array with class names and descriptions"
)

# Get detailed documentation
markdown_response = flow_rag.query(
    "Document the project architecture",
    "/path/to/your/codebase",
    user_prompt="Return as detailed Markdown documentation"
)
```

#### 4. Progress Monitoring and Advanced Options

```python
import flow_rag

# Get progress callback during indexing
def progress_callback(percentage):
    print(f"Indexing progress: {percentage}%")

flow_rag.initialize(
    "/path/to/your/codebase",
    progress_callback=progress_callback
)

# Get both answer and context
result = flow_rag.query(
    "How does the authentication flow work?",
    "/path/to/your/codebase",
    return_context=True
)

if isinstance(result, dict):
    print(f"Answer: {result.get('answer', result)}")
    print(f"Context: {result.get('context', '')[:100]}...")
```

### Examples

For complete, ready-to-run examples, see the `examples/` directory:

- **[simple_example_flowrag.py](examples/simple_example_flowrag.py)** - Basic usage example
- **[simple_context_example.py](examples/simple_context_example.py)** - Context-only mode (no LLM calls)
- **[simple_progress_example.py](examples/simple_progress_example.py)** - Progress monitoring during indexing
- **[full_example_flowrag.py](examples/full_example_flowrag.py)** - Complete feature demonstration

## Troubleshooting

Error and functionality issues can be found in the logs directory.

### Common Issues

1. **Database files location**: Database files are stored in the `rag_data` directory (or your configured `STORE_BASE_PATH`) as `{codebase_name}.db`

2. **Large files processing**: The system automatically handles large files by splitting them into chunks and combining embeddings. This may take longer for very large codebases.

3. **Method search**: The system uses intelligent boosting for method names to improve search accuracy.

.
.
.
