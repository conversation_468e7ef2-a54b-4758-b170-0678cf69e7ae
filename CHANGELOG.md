# Changelog

All notable changes to FlowRAG will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.2] - 2025-06-23

- 🐛 Fix unit tests

## [1.0.1] - 2025-06-20

- 🐛 Fix lint and type errors

## [1.0.0] - 2025-06-09

### 🚀 Major Features

#### HybridStore Search Engine

- **HybridStore**: New SQLite FTS5-based search engine replacing LanceDB
- **BM25 + Semantic Hybrid**: Intelligent combination of lexical and semantic search
- **Selective Embeddings**: Generate embeddings only when needed for better efficiency
- **Unified Schema**: Single SQLite FTS5 table for all content types (methods, classes, files)
- **Intelligent Chunking**: Automatic handling of large files by splitting into semantic chunks
- **Method Name Boosting**: Enhanced search accuracy for specific method names

#### Large Content Handling

- **Smart Chunking**: Automatic splitting of large files while preserving semantic boundaries
- **Embedding Combination**: Weighted combination of multiple embeddings for comprehensive representation
- **Token Limit Protection**: Automatic handling of content exceeding embedding provider limits
- **Chunked Embedding Cache**: Intelligent caching of combined embeddings for large content

### 🔧 Enhanced Core Features

#### Advanced Query Processing

- **Language Detection**: Automatic detection of query language context
- **Query Classification**: Smart classification of method vs class vs general queries
- **Context-Only Mode**: Retrieve relevant code context without AI processing
- **Intelligent Reranking**: Combines BM25 and semantic scores for optimal ranking

#### Optimized Reference Discovery

- **Improved Algorithm**: Enhanced reference discovery with parallel processing
- **Smart Chunking**: Adaptive chunking strategies for different file types
- **Symbol Indexing**: Efficient symbol-to-file mapping for instant lookups

### 🐛 Major Fixes and Improvements

#### Architecture Cleanup

- **Removed LanceDB Dependencies**: Complete removal of LanceDB and related schemas
- **Cleaned Orphaned Code**: Removed unused parquet file handling and embedding generation
- **Simplified Architecture**: Streamlined codebase with reduced complexity
- **Standardized Functions**: Unified all language detection functions

#### Token Limit Handling

- **Robust Token Management**: Comprehensive protection against embedding provider token limits
- **Multiple Protection Layers**: Query clipping, content chunking, and cache validation
- **Error Recovery**: Graceful handling of rate limits and API errors

### 🔄 Breaking Changes

#### API Changes

- **New Library Interface**: Updated `flow_rag_client.py` with simplified methods
- **Context-Only Queries**: New `get_context()` function for retrieving code without AI
- **Initialization Required**: Explicit `initialize()` call required before querying
- **Removed HyDE**: Simplified query processing by removing HyDE functionality

#### Configuration Updates

- **HybridStore Default**: Vector store provider now defaults to 'hybrid'
- **Simplified Config**: Removed complex LanceDB configuration options
- **New Parameters**: Added HybridStore-specific configuration parameters

#### Removed Components

- **LanceDB Support**: Complete removal of LanceDB vector store
- **Parquet Processing**: Removed intermediate parquet file generation
- **Complex Schemas**: Replaced with unified SQLite FTS5 schema

### 📦 Dependencies

#### Removed

- Complex LanceDB schema dependencies
- Parquet file processing libraries
- ANTLR VB6 parsing dependencies

#### Optimized

- Reduced overall dependency footprint
- Simplified installation process
- Better cross-platform compatibility

## [0.9.0] - 2025-06-06

### 🚀 VB6 Legacy Code Support

#### VB6 Parser Implementation

- **OptimizedVB6CodeParser**: High-performance regex-based parser for VB6 legacy systems
- **Complete VB6 Support**: Handles .frm, .bas, .cls, .vbp files with full method extraction
- **Reference Discovery**: Intelligent detection of VB6 function calls and dependencies
- **Legacy File Extensions**: Support for additional VB6 file types (.frx, .scc, .ini, .vbw, .exp)

#### Enhanced Search Capabilities

- **Hybrid Search**: Manual hybrid search combining semantic similarity with keyword matching
- **Special Files Support**: Added indexing and searching of documentation files (README.md, configs, JSON files)
- **Improved Vector Store**: Enhanced LanceDB integration with better error handling

#### Multiple Output Formats

- **Format Support**: Added support for multiple output formats (JSON, Markdown, plain text, YAML, XML, CSV)
- **Custom Formats**: Support for custom format specifications via user prompts
- **Format Detection**: Automatic format handling through enhanced prompt engineering

#### Improved Library Interface

- **Simplified API**: Enhanced `flow_rag_client.py` with easier-to-use functions
- **Output Format Parameter**: Added `output_format` parameter to main functions
- **Better Error Handling**: Improved error messages and logging throughout the system

### 🔧 Improved

#### Configuration Management

- **Centralized Config**: Improved configuration management with better defaults
- **Config Validation**: Enhanced configuration validation and error reporting
- **Environment Variables**: Better handling of environment variables and paths

#### Search and Ranking

- **Reranking Logic**: Improved result reranking with more sophisticated algorithms
- **Query Processing**: Enhanced query processing with HyDE integration
- **Context Generation**: Improved context generation for better LLM responses

#### Code Parsing

- **Parser Improvements**: Enhanced tree-sitter integration for better code parsing
- **Language Support**: Improved support for JavaScript, TypeScript, and Java
- **VB6 Integration**: Seamless integration of VB6 parser with existing architecture

### 🐛 Fixed

#### Vector Store Issues

- **LanceDB Compatibility**: Fixed compatibility issues with different LanceDB versions
- **Embedding Handling**: Improved embedding generation and storage
- **Search Fallbacks**: Better fallback mechanisms when hybrid search fails

#### VB6 Parsing

- **File Encoding**: Proper handling of VB6 file encoding and special characters
- **Method Extraction**: Accurate extraction of VB6 methods, functions, and subs
- **Reference Detection**: Improved detection of VB6 function calls and dependencies

### 📚 Documentation

#### New Documentation

- **Windows Support**: Added comprehensive Windows usage guide (`USAGE_WINDOWS.md`)
- **Updated README**: Enhanced README with new features and quick start guide
- **Usage Examples**: Added practical usage examples and demo scripts

#### Enhanced Guides

- **Installation Instructions**: Improved installation instructions for different platforms
- **API Documentation**: Better documentation of library functions and parameters
- **Troubleshooting**: Enhanced troubleshooting guides for common issues

### 🔄 Changed

#### Internal Changes

- **Code Organization**: Better code organization and separation of concerns
- **Logging**: Improved logging throughout the system for better debugging
- **Error Handling**: More robust error handling and recovery mechanisms

## [0.1.0] - 2025-05-27

### 🚀 Initial Release

#### Core Features

- Tree-sitter based code parsing for Python, JavaScript, and Java
- Vector embeddings for semantic code search using LanceDB
- Hypothetical Document Embeddings (HyDE) for query enhancement
- Reranking of results based on semantic similarity
- Hexagonal architecture with flexible adapter pattern
- Web-based chat interface for exploring codebases
- Command-line interface for indexing and querying
- Integration with CI&T Flow APIs for LLM capabilities

#### Supported Languages

- Python (.py files)
- JavaScript (.js, .jsx files)
- Java (.java files)

#### Architecture

- Domain-driven design with ports and adapters
- Modular components for easy extension
- Configurable embedding and LLM providers
- Vector database abstraction layer

#### Initial Integrations

- LanceDB for vector storage
- Jina AI for embeddings
- CI&T Flow API for LLM capabilities
- Tree-sitter for code parsing
- Cohere for reranking (optional)

---

## Legend

- 🚀 **Added**: New features
- 🔧 **Improved**: Enhancements to existing features
- 🐛 **Fixed**: Bug fixes
- 📚 **Documentation**: Documentation changes
- 🔄 **Changed**: Changes in existing functionality
- 📦 **Dependencies**: Dependency updates
- 🎯 **Migration**: Migration guides and breaking changes
