"""
flow_rag.adapters.llm_providers.factory

This module provides the FlowLLMClientProvider class, which implements the LLMPort interface and centralizes all interactions with the FlowAIClient for LLM usage in the FlowRAG library.
"""

from typing import Optional, Any, List, Dict
import logging
from flow_rag.infrastructure.log_utils import log_message
import traceback

from ...domain.ports.llm import LLMPort
from .llm_prompts import HYDE_SYSTEM_PROMPT, HYDE_V2_SYSTEM_PROMPT, CHAT_SYSTEM_PROMPT, RERANK_PROMPT, DOCUMENTATION_ANALYSIS_PROMPT

# Import FlowAPI client if available
try:
    from flow_api import FlowAPIClient
    FLOW_AVAILABLE = True
except ImportError:
    FLOW_AVAILABLE = False

# Singleton instance for FlowAPIClient to avoid multiple initializations
_flow_api_client_instance = None

def get_flow_api_client():
    """Get or create a singleton FlowAPIClient instance."""
    global _flow_api_client_instance
    if _flow_api_client_instance is None:
        _flow_api_client_instance = FlowAPIClient()
    return _flow_api_client_instance

# TODO: Implement multi-provider logic to allow selection between different LLM families

class FlowLLMClientProvider(LLMPort):
    HYDE_SYSTEM_PROMPT = HYDE_SYSTEM_PROMPT
    HYDE_V2_SYSTEM_PROMPT = HYDE_V2_SYSTEM_PROMPT
    CHAT_SYSTEM_PROMPT = CHAT_SYSTEM_PROMPT
    RERANK_PROMPT = RERANK_PROMPT
    DOCUMENTATION_ANALYSIS_PROMPT = DOCUMENTATION_ANALYSIS_PROMPT

    def __init__(self, logger: Optional[logging.Logger] = None, api_key: Optional[str] = None, model_name: str = "gpt-4o-mini") -> None:
        if not FLOW_AVAILABLE:
            log_message("FlowAPI client is not available. Check the flow_api package installation.", level="error", logger=logger, to_terminal=True)
            raise ImportError("FlowAPI library is not available")

        # Use singleton FlowAPI client to avoid multiple initializations
        self.client = get_flow_api_client()
        log_message("# FlowAPI client (singleton) initialized", level="info", logger=logger)

        self._model_name = model_name
        self.logger = logger

        # Get configuration for different model types
        from ...infrastructure.config import get_config
        config = get_config()
        llm_config = config.get_llm_config()

        # Store different model configurations
        self._default_model = llm_config.get('model_name', 'gpt-4o-mini')
        self._big_model = llm_config.get('big_model', 'gemini-2.5-pro')
        self._think_model = llm_config.get('think_model', 'anthropic.claude-37-sonnet')
        self._fast_model = llm_config.get('fast_model', 'gemini-2.0-flash')

        # Store max tokens for different models
        self._max_context_length = llm_config.get('max_tokens', 118000)
        self._big_max_tokens = llm_config.get('big_max_tokens', 1048000)
        self._think_max_tokens = llm_config.get('think_max_tokens', 200000)
        self._fast_max_tokens = llm_config.get('fast_max_tokens', 1048000)

    def check_connection(self) -> bool:
        """
        Check connection to FlowAPI and refresh token if needed.

        Returns:
            bool: True if connection is successful, False otherwise
        """
        try:
            if self.client is None:
                log_message("[FlowLLMClientProvider] Client not initialized", level="warning", logger=self.logger)
                return False

            log_message("[FlowLLMClientProvider] Checking FlowAPI connection...", logger=self.logger)

            # Check if check_connection method exists, otherwise use a lightweight test
            if hasattr(self.client, 'check_connection'):
                self.client.check_connection()
            else:
                # Fallback: just verify client is initialized (avoid actual API call)
                result = self.client is not None

            if result:
                log_message("[FlowLLMClientProvider] FlowAPI connection successful", logger=self.logger)
            else:
                log_message("[FlowLLMClientProvider] FlowAPI connection failed", level="warning", logger=self.logger)

            return result

        except Exception as e:
            log_message(f"[FlowLLMClientProvider] Error checking connection: {str(e)}", level="error", logger=self.logger)
            return False

    def answer_with_context(self, query: str, context: str) -> str:
        log_message("--- Answering with context", logger=self.logger)

        # Check if this is primarily a documentation query
        if self._is_documentation_query(query, context):
            log_message("Using documentation analysis prompt for this query", logger=self.logger)
            sys_prompt = self.DOCUMENTATION_ANALYSIS_PROMPT.format(context=context)
        else:
            log_message("Using standard chat prompt for this query", logger=self.logger)
            sys_prompt = self.CHAT_SYSTEM_PROMPT.format(context=context)

        # Determine model type based on context size
        model_type = self._select_model_for_context(context)
        log_message(f"Using {model_type} model for context processing", logger=self.logger)

        result = self.send_via_flow(user_prompt=query, system_prompt=sys_prompt, model_type=model_type)
        return result or ""

    def _is_documentation_query(self, query: str, context: str) -> bool:
        """
        Determine if a query is primarily about documentation based on query terms and context content.

        Args:
            query: The user's query
            context: The context containing search results

        Returns:
            True if this appears to be a documentation-focused query
        """
        # Documentation keywords in the query
        doc_keywords = [
            'readme', 'documentation', 'docs', 'what does', 'what is', 'overview',
            'summary', 'summarize', 'explain project', 'project description',
            'getting started', 'how to use', 'installation', 'setup', 'configure',
            'introduction', 'about', 'purpose', 'features', 'architecture overview'
        ]

        query_lower = query.lower()
        has_doc_keywords = any(keyword in query_lower for keyword in doc_keywords)

        # Check if context is primarily documentation (special files)
        has_documentation_section = "=== RELEVANT DOCUMENTATION ===" in context

        # Count lines of documentation vs code
        if has_documentation_section:
            doc_lines = context.count('\n') - context.split("=== RELEVANT DOCUMENTATION ===")[0].count('\n')
            total_lines = context.count('\n')
            doc_ratio = doc_lines / max(total_lines, 1)

            # If >50% of context is documentation OR query has doc keywords, use doc prompt
            return doc_ratio > 0.5 or has_doc_keywords

        # Fallback: use doc prompt if query clearly asks for documentation
        return has_doc_keywords

    def enhance_query(self, query: str, context: Optional[str] = None) -> str:
        log_message("--- Enhancing query", logger=self.logger)
        sys_prompt = self.HYDE_SYSTEM_PROMPT
        # Use fast_model for query enhancement (speed is more important than deep reasoning)
        result = self.send_via_flow(
            user_prompt=f"Help predict the answer to the query: {query}",
            system_prompt=sys_prompt,
            model_type="fast"
        )
        return result or ""

    def generate_context(self, *args: Any, **kwargs: Any) -> str:
        # Example: adjust as needed for real logic
        return "[FLOW CONTEXT GENERATED]"

    @property
    def max_context_length(self):
        return self._max_context_length

    @property
    def model_name(self):
        return self._model_name

    def rerank_results(self, query: str, results: List[Dict[str, Any]], content_key: str = "content") -> List[Dict[str, Any]]:
        log_message("--- Reranking results", logger=self.logger)

        if not results or len(results) <= 1:
            return results

        try:
            # Prepare content for reranking
            content_items = []
            for i, result in enumerate(results):
                content = result.get(content_key, "")
                if content:
                    content_items.append(f"Item {i+1}: {content[:500]}...")  # Limit content length

            if not content_items:
                return results

            # Create reranking prompt
            content_text = "\n\n".join(content_items)
            rerank_prompt = self.RERANK_PROMPT.format(
                query=query,
                content=content_text
            )

            # Use think_model for reranking (better reasoning)
            rerank_response = self.send_via_flow(
                user_prompt=f"Rerank these items by relevance to the query: {query}",
                system_prompt=rerank_prompt,
                model_type="think"
            )

            if rerank_response:
                log_message(f"[FlowLLMClientProvider] Reranking response: {rerank_response[:200]}...", logger=self.logger)
                # For now, return original results (implement parsing logic as needed)
                # TODO: Parse the reranking response and reorder results accordingly
                return results
            else:
                log_message("[FlowLLMClientProvider] No reranking response received, returning original order", logger=self.logger)
                return results

        except Exception as e:
            log_message(f"[FlowLLMClientProvider] Error during reranking: {str(e)}", level="error", logger=self.logger)
            return results

    def send_via_flow(self, user_prompt: str, system_prompt: str, model_type: str = "default") -> Optional[str]:
        """
        Send a prompt to FlowAPI and get a completion.

        Args:
            user_prompt: The user prompt to send
            system_prompt: System prompt to set context
            model_type: Type of model to use ("default", "big", "think", "fast")

        Returns:
            Generated text completion
        """
        # Select model based on type
        model_name = self._get_model_for_type(model_type)

        log_message(f"[FlowLLMClientProvider] Sending to FlowAPI | model: {model_name} | user_prompt: {user_prompt[:120]} ...({len(user_prompt)} chars)", logger=self.logger)

        # Try the requested model first, then fallback to default if it fails
        models_to_try = [model_name]
        if model_type != "default" and model_name != self._default_model:
            models_to_try.append(self._default_model)

        last_error = None
        for attempt_model in models_to_try:
            try:
                response = self.client.with_model(attempt_model).get_answer(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt
                )
                log_message(f"[FlowLLMClientProvider] Received from FlowAPI | model: {attempt_model} | response: {str(response)[:300]} ...({len(str(response))} chars)", logger=self.logger)
                return response
            except Exception as e:
                last_error = e
                if attempt_model != models_to_try[-1]:  # Not the last model to try
                    log_message(f"[FlowLLMClientProvider] Model {attempt_model} failed, trying fallback: {str(e)}", level="warning", logger=self.logger)
                else:
                    log_message(f"[FlowLLMClientProvider] All models failed. Last error: {str(e)}", level="error", logger=self.logger)
                    log_message(traceback.format_exc(), logger=self.logger, level="error")

        # If we get here, all models failed
        if last_error:
            raise last_error
        else:
            raise RuntimeError("All models failed without specific error")

    def _get_model_for_type(self, model_type: str) -> str:
        """
        Get the appropriate model name for the given type.

        Args:
            model_type: Type of model ("default", "big", "think", "fast")

        Returns:
            Model name string
        """
        if model_type == "big":
            return self._big_model
        elif model_type == "think":
            return self._think_model
        elif model_type == "fast":
            return self._fast_model
        else:
            return self._default_model

    def _select_model_for_context(self, context: str) -> str:
        """
        Select the appropriate model based on context size and complexity.

        Args:
            context: The context string to analyze

        Returns:
            Model type to use ("big", "think", "fast", "default")
        """
        # Estimate token count (rough approximation: 1 token ≈ 4 characters)
        estimated_tokens = len(context) // 4

        # Use big_model for very large contexts that need extensive processing
        if estimated_tokens > 50000:  # Very large context
            log_message(f"Large context detected ({estimated_tokens} estimated tokens), using big_model", logger=self.logger)
            return "big"
        # Use think_model for medium-large contexts that need good reasoning
        elif estimated_tokens > 20000:  # Medium-large context
            log_message(f"Medium-large context detected ({estimated_tokens} estimated tokens), using think_model", logger=self.logger)
            return "think"
        # Use default model for normal contexts
        else:
            log_message(f"Normal context size ({estimated_tokens} estimated tokens), using think_model for quality", logger=self.logger)
            return "think"