from typing import Any, List, Optional, Tuple
from ...adapters.file_loader import detect_language_from_extension
from ...domain.ports.code_parser import CodeParserPort

class BaseCodeParser(CodeParserPort):
    """
    Base class for code parsers. All specific parser implementations (Tree-sitter, ANTLR, etc)
    should inherit from this and implement language-specific logic.
    """
    BLACKLIST_DIR: List[str] = []
    WHITELIST_FILES: List[str] = []
    BLACKLIST_FILES: List[str] = []
    def __init__(self, logger: Optional[Any] = None):
        self.logger = logger
        self.gitignore_patterns: List[str] = []
        self.set_defaults()

    def set_defaults(self) -> None:
        self.BLACKLIST_DIR = []
        self.WHITELIST_FILES = []
        self.BLACKLIST_FILES = []

    def get_language_from_extension(self, file_ext: str) -> Optional[Any]:
        """
        Map file extension to language enum using the standard function.
        """
        return detect_language_from_extension(file_ext)

    def get_supported_languages(self) -> List[Any]:
        """
        Get list of supported languages. Override in child if needed.
        """
        return []

    def should_ignore_path(self, path: str) -> bool:
        """
        Check if a path should be ignored based on gitignore patterns.
        """
        import os
        import fnmatch
        relative_path = os.path.relpath(path)
        for pattern in self.gitignore_patterns:
            if pattern.startswith('!'):
                continue
            if not pattern.startswith('**'):
                pattern = '**/' + pattern
            if fnmatch.fnmatch(relative_path, pattern):
                return True
        return False

    def _load_gitignore_patterns(self, codebase_path: str) -> List[str]:
        """Load patterns from .gitignore file."""
        import os
        gitignore_path = os.path.join(codebase_path, '.gitignore')
        gitignore_patterns = []
        if os.path.exists(gitignore_path):
            with open(gitignore_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        if line.endswith('/'):
                            line = line[:-1]
                        gitignore_patterns.append(line)
        return gitignore_patterns

    def load_files(self, codebase_path: str) -> List[Tuple[str, Any]]:
        """
        Load all code files from the codebase path.
        Returns list of tuples (file_path, language_enum)
        """
        import os
        file_list = []
        self.gitignore_patterns = self._load_gitignore_patterns(codebase_path)
        for root, dirs, files in os.walk(codebase_path):
            dirs[:] = [d for d in dirs if d not in self.BLACKLIST_DIR and not self.should_ignore_path(os.path.join(root, d))]
            for file in files:
                file_path = os.path.join(root, file)
                file_ext = os.path.splitext(file)[1]
                if (file_ext in self.WHITELIST_FILES and
                    file not in self.BLACKLIST_FILES and
                    not self.should_ignore_path(file_path)):
                    language = self.get_language_from_extension(file_ext)
                    if language:
                        file_list.append((file_path, language))
        return file_list