"""
FlowAPI Embedding Provider for the FlowRAG library.

This module provides an implementation of the EmbeddingsPort interface
using the FlowAPI library for generating embeddings.
"""

import logging
import time
from typing import List, Optional, Dict, Any
from flow_rag.infrastructure.log_utils import log_message
from .base_embedding_provider import BaseEmbeddingProvider

# Import FlowAPI client if available
try:
    from flow_api import FlowAPIClient
    FLOWAPI_AVAILABLE = True
except ImportError:
    FLOWAPI_AVAILABLE = False


class FlowAPIEmbeddingProvider(BaseEmbeddingProvider):
    """
    Implementation of EmbeddingsPort using FlowAPI embeddings.
    All batching, clipping, etc., are handled by BaseEmbeddingProvider.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, logger: Optional[logging.Logger] = None, **kwargs: Any) -> None:
        if not FLOWAPI_AVAILABLE:
            raise ImportError("FlowAPI library is not available. Please install the flow_api package.")
        
        # Get config from centralized configuration
        config = self._get_config_manager().get_embedding_config()
        
        model_name = config.get("model_name", "text-embedding-3-small")
        embedding_dim = config.get("dimension", 1024)
        max_token_limit = config.get("max_tokens", 8191)
        
        # Store these before calling super().__init__
        self._model_name = model_name
        self.embedding_dim = embedding_dim
        self._max_token_limit = max_token_limit
        self.logger = logger or logging.getLogger(__name__)
        
        # Call parent constructor with explicit parameters
        super().__init__(
            config=config,
            logger=self.logger,
            **kwargs
        )
        
        # Override max_tokens to ensure it's set correctly
        self.max_tokens = max_token_limit
        
        # Initialize FlowAPI client
        self.client = None
        self._setup_client()
    
    def _setup_client(self):
        """Initialize the FlowAPI client."""
        try:
            self.client = FlowAPIClient()
            log_message(f"[FlowAPIEmbeddingProvider] FlowAPI client initialized with model: {self._model_name}", 
                       logger=self.logger, level="info")
        except Exception as e:
            log_message(f"[FlowAPIEmbeddingProvider] Failed to initialize FlowAPI client: {str(e)}", 
                       logger=self.logger, level="error", to_terminal=True)
            raise
    
    def get_embedding_dimensions(self) -> int:
        """Return the embedding dimensions for this provider."""
        return self.embedding_dim
    
    @property
    def model_name(self) -> str:
        """Get the name of the embedding model."""
        return self._model_name
    
    def max_token_limit(self) -> int:
        """Get the maximum number of tokens supported by this embedding model."""
        return self._max_token_limit
    
    def embed_query(self, query: str) -> List[float]:
        """
        Generate embeddings for a text query.
        
        Args:
            query: The query text to embed
            
        Returns:
            Embedding vector as a list of floats
        """
        if not query or not query.strip():
            log_message("[FlowAPIEmbeddingProvider] Empty query provided", 
                       logger=self.logger, level="warning")
            return [0.0] * self.embedding_dim
        
        # Clip text to token limit
        clipped_query = self.clip_text_to_max_tokens(query)
        
        # Get embedding using FlowAPI
        embeddings = self._get_embeddings([clipped_query])
        
        if embeddings and embeddings[0] is not None:
            return embeddings[0]
        else:
            log_message(f"[FlowAPIEmbeddingProvider] Failed to generate embedding for query: {query[:100]}...", 
                       logger=self.logger, level="error", to_terminal=True)
            return [0.0] * self.embedding_dim
    
    def _get_embeddings(self, texts: List[str]) -> List[Optional[List[float]]]:
        """
        Get embeddings for a list of texts using the FlowAPI.
        
        Args:
            texts: List of text strings to embed
            
        Returns:
            List of embedding vectors or None values for failed embeddings
        """
        if not texts:
            return []
        
        if self.client is None:
            raise RuntimeError("FlowAPI client not initialized")
        
        # Filter out empty texts and keep track of original indices
        valid_texts = []
        valid_indices = []
        for i, text in enumerate(texts):
            if text and text.strip():
                valid_texts.append(text.strip())
                valid_indices.append(i)
        
        if not valid_texts:
            return [None] * len(texts)
        
        # Initialize result list with None values
        result = [None] * len(texts)
        
        # Process embeddings with retry logic
        for attempt in range(3):
            try:
                # Small delay to avoid rate limits (except on first call)
                if attempt > 0:
                    time.sleep(0.5)
                
                embeddings = []
                for text in valid_texts:
                    try:
                        # Use FlowAPI to generate embedding
                        response = self.client.generate_embedding(
                            text=text,
                            model=self._model_name,
                            encoding_format="float"
                        )
                        
                        # Extract embedding from response
                        if response and 'data' in response and len(response['data']) > 0:
                            embedding = response['data'][0]['embedding']
                            embeddings.append(embedding)
                        else:
                            log_message(f"[FlowAPIEmbeddingProvider] Invalid response format for text: {text[:50]}...", 
                                       logger=self.logger, level="warning")
                            embeddings.append(None)
                    
                    except Exception as e:
                        log_message(f"[FlowAPIEmbeddingProvider] Error generating embedding for text: {str(e)}", 
                                   logger=self.logger, level="warning")
                        embeddings.append(None)
                
                # Map embeddings back to original positions
                for i, embedding in enumerate(embeddings):
                    if i < len(valid_indices):
                        result[valid_indices[i]] = embedding
                
                # Log success
                successful_embeddings = sum(1 for emb in embeddings if emb is not None)
                log_message(f"[FlowAPIEmbeddingProvider] Generated {successful_embeddings}/{len(valid_texts)} embeddings successfully", 
                           logger=self.logger)
                
                return result
                
            except Exception as e:
                log_message(f"[FlowAPIEmbeddingProvider] Attempt {attempt + 1} failed: {str(e)}", 
                           logger=self.logger, level="warning")
                if attempt == 2:  # Last attempt
                    log_message(f"[FlowAPIEmbeddingProvider] All attempts failed for batch of {len(texts)} texts", 
                               logger=self.logger, level="error", to_terminal=True)
                    return result
        
        return result
    
    def _create_method_embedding_text(self, method) -> str:
        """Create text representation for method embedding."""
        parts = []
        
        # Add method signature/name
        if hasattr(method, 'name') and method.name:
            parts.append(f"Method: {method.name}")
        
        # Add class context if available
        if hasattr(method, 'class_name') and method.class_name:
            parts.append(f"Class: {method.class_name}")
        
        # Add documentation if available
        if hasattr(method, 'doc_comment') and method.doc_comment:
            parts.append(f"Documentation: {method.doc_comment}")
        
        # Add source code
        if hasattr(method, 'source_code') and method.source_code:
            parts.append(f"Source Code: {method.source_code}")
        
        return "\n".join(parts)
    
    def _create_class_embedding_text(self, class_obj) -> str:
        """Create text representation for class embedding."""
        parts = []
        
        # Add class name
        if hasattr(class_obj, 'name') and class_obj.name:
            parts.append(f"Class: {class_obj.name}")
        
        # Add constructor if available
        if hasattr(class_obj, 'constructor_declaration') and class_obj.constructor_declaration:
            parts.append(f"Constructor: {class_obj.constructor_declaration}")
        
        # Add method declarations if available
        if hasattr(class_obj, 'method_declarations') and class_obj.method_declarations:
            if isinstance(class_obj.method_declarations, list):
                methods_text = ", ".join(class_obj.method_declarations)
            else:
                methods_text = str(class_obj.method_declarations)
            parts.append(f"Methods: {methods_text}")
        
        # Add source code
        if hasattr(class_obj, 'source_code') and class_obj.source_code:
            parts.append(f"Source Code: {class_obj.source_code}")
        
        return "\n".join(parts)
