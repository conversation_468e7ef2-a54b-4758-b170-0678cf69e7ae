"""
Base class for embedding providers.

This module provides an abstract base class for all embedding providers.
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Optional, Dict, Any
import logging
from flow_rag.infrastructure.log_utils import log_message
from flow_rag.infrastructure.config import get_config
import tiktoken
import traceback
from ...domain.models import Method, Class, EmbeddedMethod, EmbeddedClass
from ...domain.ports.embeddings import EmbeddingsPort
from tqdm import tqdm
import json

class BaseEmbeddingProvider(EmbeddingsPort, ABC):
    """Abstract base class for embedding providers."""
    
    def __init__(
        self,
        config: Optional[Dict[str, Any]] = None,
        logger: Optional[logging.Logger] = None,
        **kwargs: Any
    ) -> None:
        """
        Initializes the base embedding provider using only centralized config.
        Args:
            config: Centralized configuration dictionary (model_name, api_key, max_tokens, batch_size, ...)
            logger: Optional logger
            **kwargs: Additional arguments
        """
        config = self._get_config_manager().get_embedding_config()
        self.max_tokens: int = config.get('max_tokens', 8192)
        self.batch_size = config.get('batch_size')
        self.logger = logger or logging.getLogger(self.__class__.__name__)
        self._tokenizer = None
        # Initialize provider-specific client
        self._setup_client()

    def get_method_schema(self):
        """Return the schema for methods - deprecated, HybridStore uses unified FTS schema."""
        # Note: These schemas are no longer used by HybridStore
        # HybridStore uses a unified SQLite FTS5 table instead
        return {}

    def get_class_schema(self):
        """Return the schema for classes - deprecated, HybridStore uses unified FTS schema."""
        # Note: These schemas are no longer used by HybridStore
        # HybridStore uses a unified SQLite FTS5 table instead
        return {}

    def get_special_schema(self):
        """Return the schema for special files - deprecated, HybridStore uses unified FTS schema."""
        # Note: These schemas are no longer used by HybridStore
        # HybridStore uses a unified SQLite FTS5 table instead
        return {}

    def _get_config_manager(self):
        """Get the configuration manager instance."""
        return get_config()
    
    @abstractmethod
    def _setup_client(self):
        """Initialize the provider-specific client."""
        pass
    
    @property
    def tokenizer(self):
        """Lazy load tokenizer."""
        if self._tokenizer is None:
            self._tokenizer = tiktoken.get_encoding("cl100k_base")
        return self._tokenizer
    
    def _split_text_into_chunks(self, text: str, max_tokens: int) -> List[str]:
        """
        Split text into chunks that are smaller than max_tokens.
        Tries to split at sentence or line boundaries.
        
        Args:
            text: Text to split
            max_tokens: Maximum tokens per chunk
            
        Returns:
            List of text chunks, each with at most max_tokens
        """
        if not text or not text.strip():
            return []
            
        # First, encode the entire text to get tokens
        tokens = self.tokenizer.encode(text)
        
        # If the entire text is within the limit, return it as a single chunk
        if len(tokens) <= max_tokens:
            return [text]
            
        chunks = []
        current_chunk = []
        current_length = 0
        
        # First, try to split by paragraphs (double newline)
        paragraphs = text.split("\n\n") if "\n\n" in text else [text]
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
                
            # Add the double newline back if it was there
            if "\n\n" in text:
                paragraph += "\n\n"
                
            # Encode the paragraph
            para_tokens = self.tokenizer.encode(paragraph)
            
            if len(para_tokens) > max_tokens:
                # If paragraph is too long, split by sentences
                # Simple sentence splitting by periods followed by space
                for sentence in paragraph.replace('. ', '.\n').split('\n'):
                    sentence = sentence.strip()
                    if not sentence:
                        continue
                        
                    # Add the period back if it was removed
                    if not sentence.endswith('.'):
                        sentence += '.'
                        
                    # Encode the sentence
                    sent_tokens = self.tokenizer.encode(sentence)
                    
                    # If even a single sentence is too long, we need to split by words
                    if len(sent_tokens) > max_tokens:
                        # Split into words and add them one by one
                        words = sentence.split()
                        current_sentence: List[str] = []
                        current_sentence_tokens = 0
                        
                        for word in words:
                            word_tokens = self.tokenizer.encode(' ' + word if current_sentence_tokens > 0 else word)
                            
                            if current_sentence_tokens + len(word_tokens) > max_tokens and current_sentence:
                                chunks.append(' '.join(current_sentence))
                                current_sentence = [word]
                                current_sentence_tokens = len(word_tokens)
                            else:
                                current_sentence.append(word)
                                current_sentence_tokens += len(word_tokens)
                        
                        if current_sentence:
                            chunks.append(' '.join(current_sentence))
                    else:
                        # Add the sentence to the current chunk if it fits
                        if current_length + len(sent_tokens) <= max_tokens:
                            current_chunk.append(sentence)
                            current_length += len(sent_tokens)
                        else:
                            # Add current chunk to chunks and start a new one
                            if current_chunk:
                                chunks.append(' '.join(current_chunk))
                            current_chunk = [sentence]
                            current_length = len(sent_tokens)
            else:
                # If paragraph fits within max_tokens, add it to current chunk if it fits
                if current_length + len(para_tokens) <= max_tokens:
                    current_chunk.append(paragraph)
                    current_length += len(para_tokens)
                else:
                    # Add current chunk to chunks and start a new one
                    if current_chunk:
                        chunks.append(' '.join(current_chunk))
                    current_chunk = [paragraph]
                    current_length = len(para_tokens)
        
        # Add the last chunk if not empty
        if current_chunk:
            chunks.append(' '.join(current_chunk))
            
        # Final verification to ensure no chunk exceeds max_tokens
        final_chunks = []
        for chunk in chunks:
            chunk_tokens = self.tokenizer.encode(chunk)
            if len(chunk_tokens) > max_tokens:
                # If still too big, split in half and try again recursively
                half = len(chunk) // 2
                final_chunks.extend(self._split_text_into_chunks(chunk[:half], max_tokens))
                final_chunks.extend(self._split_text_into_chunks(chunk[half:], max_tokens))
            else:
                final_chunks.append(chunk)
                
        return final_chunks

    def clip_text_to_max_tokens(self, text: str) -> str:
        """
        Clip text to maximum number of tokens while preserving meaning.

        Args:
            text: Text to clip

        Returns:
            Clipped text
        """
        if not text or not text.strip():
            return text or ""
        
        max_tokens = self.max_tokens

        try:
            # Encode the text and check token count
            tokens = self.tokenizer.encode(text)

            # Add debug logging for token overflow
            if len(tokens) > max_tokens:
                log_message(f"[EmbeddingProvider] Text has {len(tokens)} tokens, exceeds limit of {max_tokens}. Clipping...", logger=self.logger, level="warning")

            if len(tokens) <= max_tokens:
                return text

            # Simple token-based truncation - decode only the tokens we need
            truncated_tokens = tokens[:max_tokens]
            try:
                clipped_text = self.tokenizer.decode(truncated_tokens)

                # Verify the result is actually within limits
                verify_tokens = self.tokenizer.encode(clipped_text)
                if len(verify_tokens) <= max_tokens:
                    return clipped_text
                else:
                    # If still too long, be more aggressive
                    safe_tokens = tokens[:max_tokens - 10]  # Leave some margin
                    return self.tokenizer.decode(safe_tokens)

            except Exception as decode_error:
                # Fallback to character-based truncation if tokenizer decode fails
                log_message(f"[EmbeddingProvider] Tokenizer decode failed, using character truncation: {str(decode_error)}", logger=self.logger, level="warning")
                return text[:max_tokens * 3]  # Conservative estimate
            
        except Exception as e:
            log_message(f"Error in clip_text_to_max_tokens: {str(e)}", logger=self.logger, level="error")
            # Fallback to conservative character-based truncation if tokenization fails
            return text[:max_tokens * 3]  # Conservative estimate: ~3 chars per token
    
    def embed_methods(self, methods: List[Method]) -> List[EmbeddedMethod]:
        """
        Generate embeddings for a list of methods.
        
        Args:
            methods: List of Method objects
            
        Returns:
            List of EmbeddedMethod objects with embeddings
        """
        embedded_methods = []
        total = len(methods)
        processed = 0
        now = datetime.now().strftime('%H:%M:%S')

        with tqdm(total=total, desc=f"* {now} Generating embeddings for methods", unit="method") as pbar:
            for method in methods:
                # Skip methods without source code
                if not getattr(method, 'source_code', None):
                    log_message(f"Skipping method {getattr(method, 'name', 'unknown')} - no source code", level='warning', logger=self.logger, to_terminal=True)
                    processed += 1
                    pbar.update(1)
                    continue
                try:
                    # Generate text for embedding
                    text = self._create_method_embedding_text(method)
                    # Clip text to token limit
                    clipped_text = self.clip_text_to_max_tokens(text)
                    # Get embedding
                    embedding = self._get_embeddings([clipped_text])[0]
                    if not embedding:
                        log_message(f"[EmbeddingProvider] Failed to generate embedding for method {getattr(method, 'name', 'unknown')}", level='warning', logger=self.logger, to_terminal=True)
                        processed += 1
                        pbar.update(1)
                        continue
                    # Handle references - ensure it's a list for EmbeddedMethod
                    references = getattr(method, 'references', [])
                    if isinstance(references, str):
                        try:
                            references = json.loads(references)
                        except json.JSONDecodeError:
                            references = []
                    embedded_method = EmbeddedMethod(
                        embeddings=embedding,  # Assign the fetched embedding
                        name=getattr(method, 'name', ''),
                        class_name=getattr(method, 'class_name', ''),
                        file_path=getattr(method, 'file_path', ''),
                        source_code=getattr(method, 'source_code', ''),
                        doc_comment=getattr(method, 'doc_comment', ''),
                        references=references,
                        vector_id=f"method_{hash(str(getattr(method, 'file_path', '')) + str(getattr(method, 'class_name', '')) + str(getattr(method, 'name', '')))}"
                    )
                    embedded_methods.append(embedded_method)
                except Exception as e:
                    log_message(f"[EmbeddingProvider] Error generating embedding for method {getattr(method, 'name', 'unknown')}: {str(e)}", level='error', logger=self.logger, to_terminal=True)
                    log_message(traceback.format_exc(), logger=self.logger, level="error")
                processed += 1
                pbar.update(1)

        tqdm.write(f"* {now} Embeddings generated for {len(embedded_methods)}/{len(methods)} methods.")
        log_message(f"[EmbeddingProvider] Successfully generated embeddings for {len(embedded_methods)}/{len(methods)} methods", logger=self.logger)
        return embedded_methods
    
    def embed_classes(self, classes: List[Class]) -> List[EmbeddedClass]:
        """
        Generate embeddings for a list of classes.
        
        Args:
            classes: List of Class objects
            
        Returns:
            List of EmbeddedClass objects with embeddings
        """
        embedded_classes = []
        total = len(classes)
        processed = 0
        now = datetime.now().strftime('%H:%M:%S')

        with tqdm(total=total, desc=f"* {now} Generating embeddings for classes", unit="class") as pbar:
            for class_obj in classes:
                # Skip classes without source code
                if not getattr(class_obj, 'source_code', None):
                    log_message(f"Skipping class {getattr(class_obj, 'name', 'unknown')} without source code", level='warning', logger=self.logger, to_terminal=True)
                    processed += 1
                    pbar.update(1)
                    continue
                try:
                    # Generate text for embedding
                    text = self._create_class_embedding_text(class_obj)
                    # Clip text to token limit
                    clipped_text = self.clip_text_to_max_tokens(text)
                    # Get embedding
                    embedding = self._get_embeddings([clipped_text])[0]
                    if not embedding:
                        log_message(f"[EmbeddingProvider] Failed to generate embedding for class {getattr(class_obj, 'name', 'unknown')}", level='warning', logger=self.logger, to_terminal=True)
                        processed += 1
                        pbar.update(1)
                        continue
                    # Handle method_declarations - ensure it's a list
                    method_declarations = getattr(class_obj, 'method_declarations', [])
                    if isinstance(method_declarations, str):
                        try:
                            method_declarations = json.loads(method_declarations)
                        except json.JSONDecodeError:
                            method_declarations = []
                    # Handle references - ensure it's a list
                    references = getattr(class_obj, 'references', [])
                    if isinstance(references, str):
                        try:
                            references = json.loads(references)
                        except json.JSONDecodeError:
                            references = []
                    embedded_class = EmbeddedClass(
                        embeddings=embedding,
                        name=getattr(class_obj, 'name', ''),
                        file_path=getattr(class_obj, 'file_path', ''),
                        source_code=getattr(class_obj, 'source_code', ''),
                        constructor_declaration=getattr(class_obj, 'constructor_declaration', ''),
                        method_declarations=method_declarations,
                        references=references,
                        vector_id=f"class_{hash(str(getattr(class_obj, 'file_path', '')) + str(getattr(class_obj, 'name', '')))}"
                    )
                    embedded_classes.append(embedded_class)
                except Exception as e:
                    log_message(f"[EmbeddingProvider] Error generating embedding for class {getattr(class_obj, 'name', 'unknown')}: {str(e)}", level='warning', logger=self.logger, to_terminal=True)
                    log_message(traceback.format_exc(), logger=self.logger, level="error")
                processed += 1
                pbar.update(1)

        tqdm.write(f"* {now} Embeddings generated for {len(embedded_classes)}/{len(classes)} classes.")
        log_message(f"[EmbeddingProvider] Successfully generated embeddings for {len(embedded_classes)}/{len(classes)} classes", logger=self.logger)
        return embedded_classes
    
    def embed_special_files(self, special_files: List[Any], batch_size: int = 5) -> List[Dict]:
        """
        Generate embeddings for special files by splitting them into chunks if necessary.
        
        Args:
            special_files: List of SpecialFile objects to embed
            batch_size: Number of chunks to process in each batch
            
        Returns:
            List of dictionaries containing embeddings and metadata for each chunk
        """
        if not special_files:
            return []
        now = datetime.now().strftime('%H:%M:%S')
        log_message(f"[EmbeddingProvider] Processing {len(special_files)} special files", logger=self.logger, to_terminal=True)
        all_chunks = []
        chunk_metadata = []
        with tqdm(total=len(special_files), desc=f"* {now} Splitting special files", unit="file") as pbar:
            for file_info in special_files:
                try:
                    if not file_info.content or not str(file_info.content).strip():
                        log_message(f"[EmbeddingProvider] Skipping empty file: {file_info.file_path}", level='warning', logger=self.logger)
                        pbar.update(1)
                        continue
                    content = str(file_info.content).strip()
                    tokens = self.tokenizer.encode(content)
                    token_count = len(tokens)
                    if token_count <= self.max_tokens * 0.9:
                        all_chunks.append(content)
                        chunk_metadata.append({
                            'file_path': file_info.file_path,
                            'file_type': file_info.file_type,
                            'chunk_index': 0,
                            'total_chunks': 1,
                            'token_count': token_count
                        })
                    else:
                        log_message(f"[EmbeddingProvider] Splitting large special file {file_info.file_path} ({token_count} tokens) into chunks", logger=self.logger)
                        chunks = self._split_text_into_chunks(content, int(self.max_tokens * 0.8))
                        for i, chunk in enumerate(chunks):
                            chunk_token_count = len(self.tokenizer.encode(chunk))
                            log_message(f"  Chunk {i+1}/{len(chunks)}: {chunk_token_count} tokens", logger=self.logger)
                            all_chunks.append(chunk)
                            chunk_metadata.append({
                                'file_path': file_info.file_path,
                                'file_type': file_info.file_type,
                                'chunk_index': i,
                                'total_chunks': len(chunks),
                                'token_count': chunk_token_count
                            })
                except Exception as e:
                    log_message(f"[EmbeddingProvider] Error processing file {getattr(file_info, 'file_path', 'unknown')}: {str(e)}", level='error', logger=self.logger, to_terminal=True)
                    log_message(traceback.format_exc(), logger=self.logger, level="error")
                finally:
                    pbar.update(1)
        if not all_chunks:
            return []
        i = 0
        results = []
        total_chunks = len(all_chunks)
        with tqdm(total=total_chunks, desc=f"* {now} Generating embeddings for chunks", unit="chunk") as pbar:
            while i < total_chunks:
                batch_tokens = 0
                current_batch_size = 0
                max_batch_tokens = int(self.max_tokens * 0.8)
                for j in range(i, min(i + 5, total_chunks)):
                    chunk_tokens = len(self.tokenizer.encode(all_chunks[j]))
                    if batch_tokens + chunk_tokens > max_batch_tokens and current_batch_size > 0:
                        break
                    batch_tokens += chunk_tokens
                    current_batch_size += 1
                if current_batch_size == 0 and i < total_chunks:
                    current_batch_size = 1
                batch_chunks = all_chunks[i:i + current_batch_size]
                batch_metadata = chunk_metadata[i:i + current_batch_size]
                try:
                    batch_embeddings = self._get_embeddings(batch_chunks)
                    for embedding, metadata, chunk_content in zip(batch_embeddings, batch_metadata, batch_chunks):
                        if embedding is not None:
                            result = {
                                'file_path': metadata['file_path'],
                                'file_type': metadata['file_type'],
                                'chunk_index': metadata['chunk_index'],
                                'total_chunks': metadata['total_chunks'],
                                'token_count': metadata['token_count'],
                                'content': chunk_content,  # MISSING: Add the actual content!
                                'embedding': embedding
                            }
                            results.append(result)
                except Exception as e:
                    log_message(f"[EmbeddingProvider] Error generating embeddings for chunks {i+1}-{i+current_batch_size}/{total_chunks}: {str(e)}", level='error', logger=self.logger, to_terminal=True)
                    log_message(traceback.format_exc(), logger=self.logger, level="error")
                    for j in range(i, i + current_batch_size):
                        try:
                            if j >= len(all_chunks):
                                break
                            single_embedding = self._get_embeddings([all_chunks[j]])
                            if single_embedding and single_embedding[0] is not None:
                                result = {
                                    'file_path': chunk_metadata[j]['file_path'],
                                    'file_type': chunk_metadata[j]['file_type'],
                                    'chunk_index': chunk_metadata[j]['chunk_index'],
                                    'total_chunks': chunk_metadata[j]['total_chunks'],
                                    'token_count': chunk_metadata[j]['token_count'],
                                    'content': all_chunks[j],  # MISSING: Add the actual content!
                                    'embedding': single_embedding[0]
                                }
                                results.append(result)
                        except Exception as inner_e:
                            log_message(f"[EmbeddingProvider] Failed to process chunk {j+1} individually: {str(inner_e)}", level='error', logger=self.logger)
                pbar.update(current_batch_size)
                i += current_batch_size
        files_processed = len({r['file_path'] for r in results})
        log_message(
            f"[EmbeddingProvider] Processed {files_processed} special files, "
            f"generated {len(results)} chunks in total",
            logger=self.logger
        )
        return results

    @abstractmethod
    def _get_embeddings(self, texts: List[str]) -> List[Optional[List[float]]]:
        """
        Get embeddings for a list of texts.
        
        Args:
            texts: List of text strings to get embeddings for
            
        Returns:
            List of embeddings (or None for failed requests)
        """
        pass

    def embed_query(self, query: str) -> List[float]:
        """
        Generate embedding for a single query string using the provider's embedding API.
        Uses simple in-memory cache to avoid repeated API calls for same queries.

        Args:
            query: The input query string.
        Returns:
            Embedding vector (list of floats)
        Raises:
            RuntimeError if embedding generation fails.
        """
        # Clip query to max tokens to prevent API errors
        clipped_query = self.clip_text_to_max_tokens(query)

        # Simple cache to avoid repeated API calls for same queries
        if not hasattr(self, '_query_cache'):
            self._query_cache: Dict[int, List[float]] = {}

        cache_key = hash(clipped_query)
        if cache_key in self._query_cache:
            log_message("[EmbeddingProvider] Using cached embedding for query", logger=self.logger)
            return self._query_cache[cache_key]

        embeddings = self._get_embeddings([clipped_query])
        if not embeddings or embeddings[0] is None:
            raise RuntimeError("Failed to generate embedding for query")

        # Cache the result (limit cache size to prevent memory issues)
        if len(self._query_cache) < 100:  # Simple cache size limit
            self._query_cache[cache_key] = embeddings[0]

        return embeddings[0]
    
    def _create_method_embedding_text(self, method: 'Method') -> str:
        """Create text representation of a method for embedding."""
        text_parts = [
            f"Method: {getattr(method, 'name', '')}",
            f"File: {getattr(method, 'file_path', '')}",
        ]
        
        # Add class name if available
        class_name = getattr(method, 'class_name', '')
        if class_name:
            text_parts.append(f"Class: {class_name}")
        
        # Add documentation if available
        doc_comment = getattr(method, 'doc_comment', '')
        if doc_comment:
            text_parts.insert(1, f"Documentation: {doc_comment}")
        
        # Add method signature if available
        signature = getattr(method, 'signature', '')
        if signature:
            text_parts.append(f"Signature: {signature}")
        
        # Add return type if available
        return_type = getattr(method, 'return_type', '')
        if return_type:
            text_parts.append(f"Returns: {return_type}")
        
        # Add source code if available
        source_code = getattr(method, 'source_code', '')
        if source_code:
            text_parts.append(f"Code:\n{source_code}")
        
        return "\n".join(part for part in text_parts if part)
    
    def _create_class_embedding_text(self, class_obj: 'Class') -> str:
        """Create text representation of a class for embedding."""
        text_parts = [
            f"Class: {getattr(class_obj, 'name', '')}",
            f"File: {getattr(class_obj, 'file_path', '')}",
        ]
        
        # Add documentation if available
        doc_comment = getattr(class_obj, 'doc_comment', '')
        if doc_comment:
            text_parts.insert(1, f"Documentation: {doc_comment}")
        
        # Add method declarations if available
        method_declarations = getattr(class_obj, 'method_declarations', [])
        if method_declarations:
            if isinstance(method_declarations, str):
                import json
                try:
                    method_list = json.loads(method_declarations)
                    method_names = [m.get('name', '') if isinstance(m, dict) else str(m) for m in method_list]
                    text_parts.append(f"Methods: {', '.join(method_names)}")
                except json.JSONDecodeError:
                    pass
            elif isinstance(method_declarations, list):
                method_names = [m.get('name', '') if isinstance(m, dict) else str(m) for m in method_declarations]
                text_parts.append(f"Methods: {', '.join(method_names)}")
        
        # Add parent class info if available
        parent_class = getattr(class_obj, 'parent_class', '')
        if parent_class:
            text_parts.append(f"Inherits from: {parent_class}")
        
        # Add constructor if available
        constructor = getattr(class_obj, 'constructor_declaration', '')
        if constructor:
            text_parts.append(f"Constructor: {constructor}")
        
        # Add source code if available
        source_code = getattr(class_obj, 'source_code', '')
        if source_code:
            text_parts.append(f"Code:\n{source_code}")
        
        return "\n".join(part for part in text_parts if part)
