"""
OpenAI Embedding Provider

This module provides an implementation of the EmbeddingsPort using OpenAI's embedding models.
"""

import os
from typing import List, Optional, Dict, Any, ClassVar
import logging
import traceback

from openai import OpenAI

from .base_embedding_provider import BaseEmbeddingProvider
from flow_rag.infrastructure.log_utils import log_message

class OpenAIEmbeddingProvider(BaseEmbeddingProvider):
    """
    OpenAI implementation of the EmbeddingsPort.
    All batching, clipping, etc., are handled by BaseEmbeddingProvider.
    """
    DEFAULT_MODEL: ClassVar[str] = "text-embedding-3-small"
    EMBEDDING_DIM_LARGE: ClassVar[int] = 3072
    EMBEDDING_DIM_SMALL: ClassVar[int] = 1024
    MAX_TOKEN_LIMIT: ClassVar[int] = 8191

    def __init__(self, config: Optional[Dict[str, Any]] = None, logger: Optional[logging.Logger] = None, **kwargs: Any) -> None:
        config = config or {}
        model_name = config.get("model_name") or self.DEFAULT_MODEL
        self._model_name = model_name or self.DEFAULT_MODEL
        self.logger = logger or logging.getLogger(__name__)
        self.api_base = config.get("api_base")
        self.organization = config.get("organization", os.getenv("OPENAI_ORGANIZATION"))
        self.timeout = config.get("timeout", 30)
        self.max_retries = config.get("max_retries", 3)
        self._client = None
        super().__init__(
            model_name=model_name,
            api_key=config.get("api_key") or os.getenv("OPENAI_API_KEY"),
            max_tokens=config.get("max_tokens", self.MAX_TOKEN_LIMIT),
            batch_size=config.get("batch_size", 32),
            **kwargs
        )
        self._setup_client()

    def get_embedding_dimensions(self) -> int:
        model_name = self.model_name  # Always a string
        if "large" in model_name:
            return self.EMBEDDING_DIM_LARGE
        return self.EMBEDDING_DIM_SMALL

    def max_token_limit(self) -> int:
        return self.MAX_TOKEN_LIMIT

    @property
    def model_name(self) -> str:
        return self._model_name or self.DEFAULT_MODEL

    def _setup_client(self):
        if not self._client:
            self._client = OpenAI(
                base_url=self.api_base,
                organization=self.organization,
                max_retries=self.max_retries,
                timeout=self.timeout
            )

    def _get_embeddings(self, texts: List[str]) -> List[Optional[List[float]]]:
        if not texts:
            return []
        for attempt in range(self.max_retries):
            try:
                if self._client is None:
                    log_message("OpenAI client not initialized", logger=self.logger, level="error")
                    break
                response = self._client.embeddings.create(
                    input=texts,
                    model=self._model_name
                )
                embeddings: List[Optional[List[float]]] = [item.embedding for item in response.data]
                if len(embeddings) == len(texts):
                    return embeddings
                log_message("Mismatch in number of embeddings returned by OpenAI API.", logger=self.logger, level="warning")
                return [None] * len(texts)
            except Exception as e:
                if attempt < self.max_retries - 1:
                    wait_time = 2 ** attempt
                    log_message(f"OpenAI API request failed. Retrying in {wait_time}s. Error: {str(e)}", logger=self.logger, level="error")
                    log_message(traceback.format_exc(), logger=self.logger, level="error")
                    import time
                    time.sleep(wait_time)
                else:
                    log_message(f"Failed to get OpenAI embeddings after {self.max_retries} attempts: {str(e)}", logger=self.logger, level="error")
                    log_message(traceback.format_exc(), logger=self.logger, level="error")
                    return [None] * len(texts)
        return [None] * len(texts)

