"""
Utility functions for file handling and language detection.
"""
import os
import fnmatch
import logging
from typing import List, Literal, Optional, Union, Dict, Any

from flow_rag.domain.ports.file_loader import LanguageEnum, EXTENSION_MAP, FILE_FILTERS, SPECIAL_FILE_TYPES


def get_file_language(file_path: str) -> Optional[LanguageEnum]:
    """
    Detect the programming language of a file based on its extension.

    Args:
        file_path: Path to the file

    Returns:
        LanguageEnum if the extension matches a known language, None otherwise
    """
    _, ext = os.path.splitext(file_path)
    ext = ext.lower()

    # Return the language if the extension is in the EXTENSION_MAP
    return EXTENSION_MAP.get(ext)


def get_file_language_string(file_path: str) -> str:
    """
    Detect the programming language of a file and return as string.

    Args:
        file_path: Path to the file

    Returns:
        Language name as string, 'unknown' if not recognized
    """
    language_enum = get_file_language(file_path)
    if language_enum:
        return language_enum.value
    return 'unknown'


def detect_language_from_extension(file_extension: str) -> Optional[LanguageEnum]:
    """
    Detect language from file extension directly.

    Args:
        file_extension: File extension (with or without dot)

    Returns:
        LanguageEnum if the extension matches a known language, None otherwise
    """
    # Ensure extension starts with dot
    if not file_extension.startswith('.'):
        file_extension = '.' + file_extension

    file_extension = file_extension.lower()
    return EXTENSION_MAP.get(file_extension)


def load_files_generic(
    codebase_path: str,
    mode: Literal['code', 'special'],
    logger: Optional[logging.Logger] = None,
    return_specials_content: bool = False,
) -> List[Union[str, Dict[str, Any]]]:
    """
    Generic utility to load and filter files from a directory, honoring blacklists, whitelists and gitignore patterns.

    Args:
        codebase_path: Path to the codebase root
        mode: 'code' to return code files, 'special' to return only special files
        logger: Optional logger
        return_specials_content: If True and mode='special', returns the content of each special file
    Returns:
        - If mode == 'code': list of code file paths
        - If mode == 'special': list of dicts with keys 'file_path', 'file_type', and optionally 'content'
    Raises:
        ValueError: if mode is invalid
    """
    if mode not in ['code', 'special']:
        raise ValueError("Mode must be either 'code' or 'special'")
    
    if not logger:
        logger = logging.getLogger(__name__)
    
    codebase_path = os.path.abspath(codebase_path)
    if not os.path.isdir(codebase_path):
        logger.error(f"[File Loader] Invalid codebase path: {codebase_path}")
        return []

    # Load .gitignore patterns
    gitignore_patterns = _load_gitignore_patterns(codebase_path)
    
    # Get filters
    blacklist_dirs = set(FILE_FILTERS["BLACKLIST_DIRS"])
    whitelist_exts = set(FILE_FILTERS["WHITELIST_EXTS"])
    blacklist_files = set(FILE_FILTERS["BLACKLIST_FILES"])
    special_file_exts = set(FILE_FILTERS["SPECIAL_FILE_EXTS"])
    
    result: List[Union[str, Dict[str, Any]]] = []
    
    for root, dirs, files in os.walk(codebase_path, topdown=True):
        # Skip hidden directories and blacklisted directories
        dirs[:] = [
            d for d in dirs 
            if not d.startswith('.') and 
               d not in blacklist_dirs and 
               not _should_ignore_path(os.path.join(root, d), gitignore_patterns, codebase_path)
        ]
        
        for file in files:
            file_path = os.path.join(root, file)
            rel_path = os.path.relpath(file_path, codebase_path)
            
            # Skip hidden files, blacklisted files, and files in hidden directories
            if (any(part.startswith('.') for part in rel_path.split(os.sep)) or
                file in blacklist_files or
                _should_ignore_path(file_path, gitignore_patterns, codebase_path)):
                continue
            
            # Get file extension
            _, ext = os.path.splitext(file)
            ext = ext.lower()
            
            if mode == 'code':
                # Only include files with whitelisted extensions
                if ext in whitelist_exts:
                    result.append(file_path)
            else:  # mode == 'special'
                # Only include files with special extensions
                if ext in special_file_exts:
                    # Get the file type from the SPECIAL_FILE_TYPES mapping
                    file_type = SPECIAL_FILE_TYPES.get(ext, ext.lstrip('.'))
                    content = None
                    if return_specials_content:
                        content = _read_file_with_fallback_encoding(file_path, logger)
                    if return_specials_content:
                        result.append({
                            'file_path': file_path,
                            'file_type': file_type,
                            'content': content
                        })
                    else:
                        result.append({
                            'file_path': file_path,
                            'file_type': file_type
                        })
    
    return result


def _read_file_with_fallback_encoding(file_path: str, logger: Optional[logging.Logger] = None) -> Optional[str]:
    """Read a file trying multiple encodings with robust fallback."""
    encodings = ['utf-8', 'windows-1252', 'iso-8859-1', 'cp1252', 'latin1']

    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()
        except UnicodeDecodeError:
            continue
        except Exception as e:
            if logger:
                logger.error(f"[File Loader] Error reading special file {file_path}: {str(e)}")
            return None

    # Final fallback: read as binary and decode with error handling
    try:
        with open(file_path, 'rb') as f:
            raw_content = f.read()
        # Try to decode with error replacement
        content = raw_content.decode('utf-8', errors='replace')
        if logger:
            logger.warning(f"[File Loader] Read {file_path} with character replacement due to encoding issues")
        return content
    except Exception as e:
        if logger:
            logger.error(f"[File Loader] Failed to read {file_path}: {str(e)}")
        return None


def _load_gitignore_patterns(codebase_path: str) -> List[str]:
    """
    Load patterns from .gitignore file if it exists in the codebase root.
    """
    patterns = []
    gitignore_path = os.path.join(codebase_path, '.gitignore')
    if os.path.exists(gitignore_path):
        try:
            with open(gitignore_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        # Remove trailing slash for directories
                        if line.endswith('/'):
                            line = line[:-1]
                        patterns.append(line)
        except Exception as e:
            logging.getLogger(__name__).error(f"[File Loader] Failed to read .gitignore: {str(e)}")
    return patterns


def _should_ignore_path(path: str, gitignore_patterns: List[str], base_path: str) -> bool:
    """Check if a path should be ignored based on gitignore patterns."""
    rel_path = os.path.relpath(path, base_path)
    rel_dir = os.path.dirname(rel_path)
    
    for pattern in gitignore_patterns:
        if fnmatch.fnmatch(rel_path, pattern) or \
           fnmatch.fnmatch(os.path.basename(rel_path), pattern) or \
           any(fnmatch.fnmatch(part, pattern) for part in rel_dir.split(os.sep)):
            return True
    return False
