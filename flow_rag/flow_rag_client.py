"""
FlowRAG Client - Simplified interface for using the FlowRAG library

This module provides a simplified interface for using the FlowRAG library,
allowing codebases to be indexed and queried with a single function call.

The library supports two main modes:
1. Full RAG mode: Retrieves context and generates LLM answers
2. Context-only mode: Retrieves only the relevant context for custom processing

Example usage:

    # Full RAG mode
    import flow_rag
    answer = flow_rag.flow_rag("How does authentication work?", "/path/to/codebase")

    # Context-only mode
    context = flow_rag.get_context("authentication functions", "/path/to/codebase")
    custom_prompt = f"Based on this code: {context}, please..."
"""

import os
import logging
from typing import Optional, Dict, Any, Union, Callable

# Cache to store services already initialized for a codebase
_query_services_cache: Dict[str, Dict[str, Any]] = {}
_indexed_codebases: set[str] = set()

def _format_query_with_output_instructions(query: str, output_format: str) -> str:
    """
    Append format instructions to the user query.

    Args:
        query: Original user query
        output_format: Desired output format

    Returns:
        Query with format instructions appended
    """
    # Predefined format instructions
    format_instructions = {
        "json": "\n\nIMPORTANT: Provide your response in valid JSON format only. No additional text or explanations outside the JSON structure.",
        "markdown": "\n\nIMPORTANT: Format your response using proper Markdown syntax with headers, code blocks, and formatting.",
        "plain": "\n\nIMPORTANT: Provide your response in plain text format without any special formatting or markup.",
        "yaml": "\n\nIMPORTANT: Provide your response in valid YAML format only. No additional text outside the YAML structure.",
        "xml": "\n\nIMPORTANT: Provide your response in valid XML format only. No additional text outside the XML structure.",
        "csv": "\n\nIMPORTANT: Provide your response in CSV format with appropriate headers and comma-separated values.",
        "list": "\n\nIMPORTANT: Provide your response as a simple bulleted list with one item per line.",
        "code": "\n\nIMPORTANT: Provide only the code implementation without explanations or additional text.",
        "summary": "\n\nIMPORTANT: Provide a concise summary in 2-3 sentences maximum.",
        "detailed": "\n\nIMPORTANT: Provide a comprehensive and detailed explanation with examples and context."
    }

    # Use predefined instruction or treat as custom format
    if output_format.lower() in format_instructions:
        instruction = format_instructions[output_format.lower()]
    else:
        # Custom format - append as-is with formatting instruction
        instruction = f"\n\nIMPORTANT: Format your response as: {output_format}"

    return query + instruction

# Utility: check if codebase index exists
def _codebase_index_exists(vector_store_dir, codebase_name):
    """
    Checks if the codebase index exists for HybridStore.

    Returns True if a valid HybridStore database file is found.
    """
    # Check for HybridStore database file
    db_path = os.path.join(vector_store_dir, f"{codebase_name}.db")
    if os.path.exists(db_path):
        # Check if file has meaningful size (not just empty)
        if os.path.getsize(db_path) > 8192:  # At least 8KB (SQLite header + some data)
            return True
    return False

def initialize(
    codebase_path: Optional[str] = None,
    reindex: bool = False,
    logger: Optional[logging.Logger] = None,
    progress_callback: Optional[Callable[[int], None]] = None
) -> None:
    """
    Initializes the FlowRAG components for a given codebase.

    Args:
        codebase_path: Path to the codebase to be indexed/queried.
                      If None, only initializes the basic components.
        reindex: If True, forces reindexing even if the codebase is already indexed.
        logger: Optional logger for logging messages.
        progress_callback: Optional callback function that receives progress percentage (0-100).
                          Called during indexing to report progress. Example: lambda p: print(f"{p}%")
    """
    global _query_services_cache, _indexed_codebases

    # Import dependencies inside the function to avoid circular imports
    from .infrastructure.config import get_config
    from .infrastructure.log_utils import setup_logger, log_message
    from .adapters.embedding_providers import EmbeddingProviderFactory
    from .adapters.vector_stores import VectorStoreFactory
    from .adapters.llm_providers import FlowLLMClientProvider
    from .application.query_processing import QueryProcessingService
    from .application.code_indexing import CodeIndexingService

    # Get configuration instance once
    config_manager = get_config()

    # Configure logger if not provided
    if logger is None:
        logs_config = config_manager.get_logging_config()
        logs_dir = logs_config.get('base_path')
        if logs_dir:
            os.makedirs(logs_dir, exist_ok=True)
        logger = setup_logger(name="flow_rag", log_file="flow_rag.log")

    # If no codebase, only initialize basic components
    if codebase_path is None:
        log_message("Initializing FlowRAG without a specific codebase", logger=logger)
        return

    # Normalize codebase path
    codebase_path = os.path.normpath(os.path.abspath(codebase_path))

    # Validate that the codebase path exists
    if not os.path.exists(codebase_path):
        raise ValueError(f"Invalid codebase path: {codebase_path} does not exist")

    if not os.path.isdir(codebase_path):
        raise ValueError(f"Invalid codebase path: {codebase_path} is not a directory")

    codebase_name = os.path.basename(codebase_path)

    # Check if already initialized in cache and doesn't need reindexing
    if codebase_path in _query_services_cache and not reindex:
        log_message(f"FlowRAG already initialized for {codebase_name}", logger=logger, to_terminal=True)
        return

    # Get vector store configuration
    vector_store_config = config_manager.get_vector_store_config()
    vector_store_dir = vector_store_config.get('base_path')

    # Helper function to create and cache services
    def _create_and_cache_services():
        embedding_provider = EmbeddingProviderFactory.create_provider(logger=logger)
        llm_provider = FlowLLMClientProvider(logger=logger)
        vector_store = VectorStoreFactory.create_store(logger=logger)
        db_uri = os.path.join(vector_store_dir or "", codebase_name)
        vector_store.connect(db_uri)
        query_service = QueryProcessingService(
            embeddings_provider=embedding_provider,
            vector_store=vector_store,
            llm_provider=llm_provider,
            logger=logger
        )
        _query_services_cache[codebase_path] = {
            'query_service': query_service,
            'llm_provider': llm_provider,
            'vector_store': vector_store,
            'embedding_provider': embedding_provider
        }
        return embedding_provider, llm_provider, vector_store, query_service

    # Check if index already exists on disk (robust across restarts)
    if not reindex and _codebase_index_exists(vector_store_dir, codebase_name):
        log_message(f"Index for codebase {codebase_name} already exists on disk. Skipping reindexing.", logger=logger, to_terminal=True)
        # Still initialize providers and cache for querying
        _create_and_cache_services()
        log_message(f"FlowRAG initialized successfully for {codebase_name}", level="success", logger=logger)
        return

    log_message(f"Initializing FlowRAG for codebase: {codebase_name}", logger=logger)

    # Create providers and services
    embedding_provider, _, vector_store, _ = _create_and_cache_services()

    # Check if needs indexing
    if reindex or codebase_path not in _indexed_codebases:
        log_message(f"Indexing codebase: {codebase_name}", logger=logger)

        # Create file loader and parser
        from .adapters.code_parsers.factory import CodeParserFactory
        from .adapters.file_loader import FileLoader

        # Create file loader and detect language
        file_loader = FileLoader(logger=logger)
        files = file_loader.load_files(codebase_path)

        if not files:
            log_message("No supported files found in the codebase. Indexing aborted.",
                      level="error", logger=logger, to_terminal=True)
            return

        # Get the language of majority of files
        languages = [f[1] for f in files]
        language = max(set(languages), key=languages.count)
        log_message(f"Main language detected: '{language.value}'", logger=logger)

        # Check if language is supported
        from flow_rag.domain.ports.file_loader import LanguageEnum
        supported_languages = [lang for lang in LanguageEnum if lang != LanguageEnum.UNKNOWN]
        if language not in supported_languages:
            log_message(f"Language '{language}' is not supported. Indexing aborted.",
                      level="error", logger=logger, to_terminal=True)
            return

        # Create parser for the detected language
        code_parser = CodeParserFactory.create_parser(language, logger=logger)
        indexing_service = CodeIndexingService(
            code_parser=code_parser,
            embeddings_provider=embedding_provider,
            vector_store=vector_store,
            logger=logger,
            progress_callback=progress_callback
        )
        # Index the codebase
        indexing_service.index_codebase(codebase_path)
        _indexed_codebases.add(codebase_path)
        log_message(f"Codebase {codebase_name} indexed successfully", level="success", logger=logger)

    log_message(f"FlowRAG initialized successfully for {codebase_name}", level="success", logger=logger)

def query(
    query_text: str,
    codebase_path: Optional[str] = None,
    return_context: bool = False,
    context_only: bool = False,
    output_format: Optional[str] = None
) -> Union[str, Dict[str, Any]]:
    """
    Performs a query on the indexed codebase.

    Args:
        query_text: Text of the query.
        codebase_path: Path to the codebase to be queried.
                      If None, uses the last initialized codebase.
        return_context: If True, returns the complete context along with the answer.
        context_only: If True, returns only the context without generating an LLM answer.
        output_format: Optional format specification for the response. Examples:
            - "json" - Return response as JSON
            - "markdown" - Return response as markdown
            - "plain" - Return response as plain text (default)
            - Custom format string to be appended to the query

    Returns:
        If context_only=True: String with the formatted context ready for use in prompts.
        If return_context=False: String with the answer in the specified format.
        If return_context=True: Dictionary with 'answer' and 'context'.
    """
    global _query_services_cache, _indexed_codebases
    
    # If codebase_path is None, uses the first available
    if codebase_path is None:
        if not _query_services_cache:
            raise ValueError("No codebase was initialized. Call initialize() first.")
        codebase_path = next(iter(_query_services_cache.keys()))
    else:
        codebase_path = os.path.normpath(os.path.abspath(codebase_path))

        # Validate that the codebase path exists
        if not os.path.exists(codebase_path):
            raise ValueError(f"Invalid codebase path: {codebase_path} does not exist")

        if not os.path.isdir(codebase_path):
            raise ValueError(f"Invalid codebase path: {codebase_path} is not a directory")

    # Check if codebase is initialized
    if codebase_path not in _query_services_cache:
        # Initializes automatically
        initialize(codebase_path)
        
    # Get services from cache
    services = _query_services_cache[codebase_path]
    query_service = services['query_service']
    llm_provider = services['llm_provider']
    
    # Process the query
    result = query_service.process_query(query_text)

    # If context_only is True, return only the context string
    if context_only:
        return result.context

    # Prepare the final query with format instructions if specified
    final_query = query_text
    if output_format:
        final_query = _format_query_with_output_instructions(query_text, output_format)

    # Generate the answer
    answer = llm_provider.answer_with_context(
        query=final_query,
        context=result.context
    )

    # Return the result
    if return_context:
        return {
            'answer': answer,
            'context': result.context,
            'metadata': result.metadata
        }
    else:
        return answer

def get_context(
    query_text: str,
    codebase_path: Optional[str] = None
) -> str:
    """
    Retrieves only the context for a query without generating an LLM answer.

    This function is useful when you need to find relevant code snippets
    and documentation for a query, and then apply them in a custom prompt
    or processing pipeline.

    Args:
        query_text: Text of the query to search for relevant context.
        codebase_path: Path to the codebase to be queried.
                      If None, uses the last initialized codebase.

    Returns:
        String containing the formatted context ready for use in prompts.
        The context includes relevant methods, classes, and documentation
        formatted in a structured way.

    Example:
        ```python
        import flow_rag

        # Initialize the codebase
        flow_rag.initialize("/path/to/codebase")

        # Get context for a query
        context = flow_rag.get_context("functions related to user authentication")

        # Use the context in your custom prompt
        custom_prompt = f"Based on this code context: {context}, please..."
        ```
    """
    result = query(
        query_text=query_text,
        codebase_path=codebase_path,
        context_only=True
    )
    # Since context_only=True, query() returns str, not Dict
    return str(result)

def flow_rag(
    query: Optional[str] = None,
    codebase_path: Optional[str] = None,
    reindex: bool = False,
    return_context: bool = False,
    context_only: bool = False,
    output_format: Optional[str] = None,
    logger: Optional[logging.Logger] = None,
    progress_callback: Optional[Callable[[int], None]] = None
) -> Union[str, Dict[str, Any], None]:
    """
    Simplified function to use FlowRAG.

    Args:
        query: Text of the query. If None, only initializes the codebase.
        codebase_path: Path to the codebase to be indexed/queried.
        reindex: If True, forces reindexing even if the codebase is already indexed.
        return_context: If True, returns the complete context along with the answer.
        context_only: If True, returns only the context without generating an LLM answer.
        output_format: Optional format specification for the response (json, markdown, plain, etc.).
        logger: Optional logger for logging messages.
        progress_callback: Optional callback function that receives progress percentage (0-100).
                          Called during indexing to report progress. Example: lambda p: print(f"{p}%")

    Returns:
        If query=None: None
        If context_only=True: String with the formatted context ready for use in prompts.
        If return_context=False: String with the answer in the specified format.
        If return_context=True: Dictionary with 'answer' and 'context'.
    """
    # Initialize FlowRAG for the specified codebase
    initialize(
        codebase_path=codebase_path,
        reindex=reindex,
        logger=logger,
        progress_callback=progress_callback
    )

    # If no query, only returns None
    if query is None:
        return None

    # Performs the query using the module-level query function
    query_function = globals()['query']
    return query_function(
        query_text=query,
        codebase_path=codebase_path,
        return_context=return_context,
        context_only=context_only,
        output_format=output_format
    )
