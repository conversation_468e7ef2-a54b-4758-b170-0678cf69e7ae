"""
FlowAPI Singleton Manager

Centralizes FlowAPIClient instance management to avoid multiple initializations
and reduce redundant API calls.
"""

# Import FlowAPI client if available
try:
    from flow_api import FlowAPIClient
    FLOWAPI_AVAILABLE = True
except ImportError:
    FLOWAPI_AVAILABLE = False

# Global singleton instance
_flow_api_client_instance = None

def get_flow_api_client():
    """
    Get or create a singleton FlowAPIClient instance.
    
    This ensures only one FlowAPIClient is created across the entire application,
    reducing initialization overhead and avoiding redundant connections.
    
    Returns:
        FlowAPIClient: The singleton instance
    """
    global _flow_api_client_instance
    
    if not FLOWAPI_AVAILABLE:
        raise ImportError("FlowAPI library is not available. Please install the flow_api package.")
    
    if _flow_api_client_instance is None:
        _flow_api_client_instance = FlowAPIClient()
    
    return _flow_api_client_instance

def reset_flow_api_client():
    """
    Reset the singleton instance (useful for testing).
    """
    global _flow_api_client_instance
    _flow_api_client_instance = None
