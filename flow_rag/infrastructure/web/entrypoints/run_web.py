#!/usr/bin/env python3
"""
Standalone script to run the FlowRAG web interface.

This script provides a simple way to start the web interface
for an already indexed codebase.
"""

import os
import sys
import argparse
from typing import Any
from flow_rag.infrastructure.log_utils import setup_logger, log_message
import traceback

# Add the current directory to sys.path to enable imports
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../"))
sys.path.insert(0, project_root)
os.environ["PYTHONPATH"] = project_root


def _is_codebase_indexed(codebase_path: str, logger: Any) -> bool:
    """
    Check if a codebase has been indexed by verifying the existence of vector store data.

    Args:
        codebase_path: Path to the codebase directory
        logger: Logger instance

    Returns:
        True if the codebase appears to be indexed, False otherwise
    """
    try:
        # Import config to get vector store path
        from flow_rag.infrastructure.config import get_config

        config_manager = get_config()
        vector_store_config = config_manager.get_vector_store_config()

        # Get the expected vector store path for this codebase
        codebase_name = os.path.basename(os.path.abspath(codebase_path))
        STORE_BASE_PATH = vector_store_config.get('base_path')
        if STORE_BASE_PATH is None:
            log_message("Vector store base_path not configured", level="error", logger=logger)
            return False
        vector_store_path = os.path.join(STORE_BASE_PATH, codebase_name)

        # Check if vector store directory exists and has content
        if not os.path.exists(vector_store_path):
            log_message(f"Vector store directory not found: {vector_store_path}", level="debug", logger=logger)
            return False

        # Check for HybridStore database file
        db_path = os.path.join(STORE_BASE_PATH, f"{codebase_name}.db")
        has_indexed_data = False

        if os.path.exists(db_path):
            # Check if file has meaningful size (not just empty)
            if os.path.getsize(db_path) > 8192:  # At least 8KB (SQLite header + some data)
                has_indexed_data = True
                log_message(f"Found HybridStore indexed data: {db_path}", logger=logger)

        if not has_indexed_data:
            log_message(f"No indexed data found in: {STORE_BASE_PATH}", level="warning", logger=logger)
            return False

        log_message(f"Codebase appears to be indexed: {codebase_path}", logger=logger)
        return True

    except Exception as e:
        log_message(f"Error checking if codebase is indexed: {str(e)}", level="warning", logger=logger)
        # If we can't determine, assume it's not indexed to be safe
        return False


def main():
    """Main function to run the web server."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Run the FlowRAG web interface")
    parser.add_argument("codebase_path", help="Path to the codebase that has been indexed")
    parser.add_argument("--port", type=int, default=5001, help="Port to run the web server on")
    parser.add_argument("--host", default="localhost", help="Host to bind the web server to")
    parser.add_argument("--debug", action="store_true", help="Run in debug mode")
    args = parser.parse_args()
    
    # Set up logging
    logger = setup_logger(name="flowrag_web", log_file="flowrag_web.log")
    
    # Verify the codebase path exists
    if not os.path.exists(args.codebase_path):
        log_message(f"Codebase path does not exist: {args.codebase_path}", level="error", logger=logger, to_terminal=True)
        sys.exit(1)

    # Verify the codebase has been indexed
    if not _is_codebase_indexed(args.codebase_path, logger):
        log_message(f"Codebase has not been indexed yet: {args.codebase_path}", level="error", logger=logger, to_terminal=True)
        log_message("Please run the indexing process first before starting the web interface.", level="error", logger=logger, to_terminal=True)
        sys.exit(1)
    
    # Export PYTHONPATH to include the current directory
    os.environ["PYTHONPATH"] = os.path.dirname(os.path.abspath(__file__))

    log_message(f"\n\nStarting web server for codebase: \"{args.codebase_path}\"", logger=logger)
    
    # Create and run the web app
    from flow_rag.infrastructure.web.app import WebApp

    # Pre-connect to FlowAPI to avoid delays on first request
    from flow_rag.flow_rag_client import check_connection
    
    try:
        webapp = WebApp(args.codebase_path)
        webapp.run()
        check_connection()
    except KeyboardInterrupt:
        log_message("Server stopped by user", logger=logger, to_terminal=True)
    except Exception as e:
        log_message(f"Error running server: {str(e)}", level="error", logger=logger, to_terminal=True)
        log_message(traceback.format_exc(), logger=logger, level="error")
        sys.exit(1)

if __name__ == "__main__":
    main()