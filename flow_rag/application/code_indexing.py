from typing import List, Dict, Any, Optional, Callable
import os
import logging
from flow_rag.infrastructure.log_utils import log_message
from flow_rag.application.utils import get_db_uri, update_progress, calculate_progress

from flow_rag.adapters.file_loader import get_file_language, load_files_generic
from flow_rag.domain.ports.file_loader import LanguageEnum
from ..domain.models import Method, Class, SpecialFile, CodebaseMetadata
from ..domain.ports.code_parser import CodeParserPort
from ..domain.ports.embeddings import EmbeddingsPort
from ..domain.ports.vector_store import VectorStorePort
from ..infrastructure.config import get_config
from ..utils.reference_optimizer import OptimizedReferenceDiscovery, Symbol
from ..utils.smart_chunking import SmartChunker, ChunkingConfig
from ..utils.simple_reference_discovery import create_simple_reference_discovery

class CodeIndexingService:
    """
    Application service for indexing codebase content.
    
    This service coordinates the process of:
    1. Parsing code from a codebase
    2. Generating embeddings for the parsed entities
    3. Storing the embeddings in a vector database
    """
    
    def __init__(
        self,
        code_parser: CodeParserPort,
        embeddings_provider: EmbeddingsPort,
        vector_store: VectorStorePort,
        logger: Optional[logging.Logger] = None,
        progress_callback: Optional[Callable[[int], None]] = None
    ) -> None:
        self.code_parser = code_parser
        self.embeddings_provider = embeddings_provider
        self.vector_store = vector_store
        self.logger = logger
        self.progress_callback = progress_callback
        self.last_progress = 0  # Track last progress to avoid duplicates

    def _get_config_manager(self):
        """Get the configuration manager instance."""
        return get_config()

    def _update_progress(self, percentage: int) -> None:
        """Update progress if callback is provided."""
        self.last_progress = update_progress(
            self.progress_callback,
            percentage,
            self.last_progress,
            self.logger
        )

    def _calculate_progress(self, current_step: str, current_item: int = 0, total_items: int = 0) -> int:
        """Calculate progress percentage based on current step and item progress."""
        return calculate_progress(current_step, current_item, total_items)


    
    def index_codebase(self, codebase_path: str) -> CodebaseMetadata:
        """
        Index a codebase by parsing its content and storing embeddings.

        Args:
            codebase_path: Path to the codebase root directory

        Returns:
            CodebaseMetadata with information about the indexed codebase
        """

        # Normalize path
        codebase_path = os.path.normpath(os.path.abspath(codebase_path))
        codebase_name = os.path.basename(codebase_path)

        # Create metadata object
        metadata = CodebaseMetadata(
            name=codebase_name,
            path=codebase_path
        )

        # Progress: 0% - Starting
        self._update_progress(self._calculate_progress('start'))

        # Connect to vector store
        db_uri = self._get_db_uri(codebase_name)
        self.vector_store.connect(db_uri)
        
        # Load and parse files
        log_message(f"[CodeIndexingService] Loading files from codebase: \"{codebase_path}\"", logger=self.logger)
        # Use generic loader for code files
        file_list = load_files_generic(
            codebase_path=codebase_path,
            mode='code',
            logger=self.logger
        )

        # Progress: Files loaded
        self._update_progress(self._calculate_progress('files_loaded'))

        # Update metadata languages
        for file_path in file_list:
            if isinstance(file_path, str):
                language = get_file_language(file_path)
                if language:
                    metadata.add_language(language)
        
        log_message(f"[CodeIndexingService] Parsing {len(file_list)} files", logger=self.logger)
        # Prepare list of files with their languages for the parser
        file_languages = []
        for f in file_list:
            if isinstance(f, str):
                lang = get_file_language(f)
                if lang:
                    # Always ensure lang is LanguageEnum
                    if not isinstance(lang, LanguageEnum):
                        try:
                            lang = LanguageEnum(lang)
                        except ValueError:
                            continue  # skip unknown language
                    file_languages.append((f, lang))
        classes, methods, class_names, method_names = self.code_parser.parse_code_files(file_languages)
        metadata.class_count = len(classes)
        metadata.method_count = len(methods)

        # Progress: Code parsed
        self._update_progress(self._calculate_progress('code_parsed'))

        log_message("[CodeIndexingService] Finding references between code entities using optimized discovery", logger=self.logger)
        # Use optimized reference discovery instead of the slow O(n²) approach
        references = self._find_references_optimized(file_languages, classes, methods, class_names, method_names)

        self._update_references(classes, methods, references)

        # Progress: References found
        self._update_progress(self._calculate_progress('references_found'))
        
        # Process special files (markdown, shell scripts, etc.)
        log_message("[CodeIndexingService] Finding special files", logger=self.logger)
        special_files_raw = load_files_generic(
            codebase_path=codebase_path,
            mode='special',
            logger=self.logger,
            return_specials_content=True
        )
        special_files = []
        for sf in special_files_raw:
            if isinstance(sf, dict):
                special_files.append(SpecialFile(
                    file_path=sf.get('file_path', ''),
                    content=sf.get('content', ''),
                    file_type=sf.get('file_type', '')
                ))
        total_code_files = len(file_list)
        total_special_files = len(special_files)
        log_message(f"[CodeIndexingService] Found {total_special_files} special files", logger=self.logger)
        
        # Update total file count in metadata
        metadata.file_count = total_code_files + total_special_files
        log_message(f"[CodeIndexingService] Total files processed: {metadata.file_count} ({total_code_files} code files + {total_special_files} special files)", logger=self.logger)

        # Progress: Special files processed
        self._update_progress(self._calculate_progress('special_files_processed'))

        # Store content directly in HybridStore (optimized flow)
        self._store_content_directly(methods, classes, special_files)

        # Progress: Complete
        self._update_progress(self._calculate_progress('complete'))

        return metadata

    def _store_content_directly(self, methods: list, classes: list, special_files: list) -> None:
        """
        Store content directly in HybridStore without generating embeddings.
        This is much faster than the traditional embedding-based approach.
        """
        log_message("[CodeIndexingService] Storing content directly in HybridStore (no embeddings needed)", logger=self.logger)

        # Store methods
        if methods:
            method_data = []
            for method in methods:
                # Convert method object to dict format expected by HybridStore
                method_dict = {
                    'name': getattr(method, 'name', ''),
                    'source_code': getattr(method, 'source_code', ''),
                    'file_path': getattr(method, 'file_path', ''),
                    'class_name': getattr(method, 'class_name', ''),
                    'doc_comment': getattr(method, 'doc_comment', ''),
                    'references': getattr(method, 'references', [])
                }
                method_data.append(method_dict)

            success = self.vector_store.add_vectors("_method", method_data)
            log_message(f"[CodeIndexingService] Methods storage {'succeeded' if success else 'failed'} ({len(method_data)} items)", logger=self.logger)

        # Store classes
        if classes:
            class_data = []
            for cls in classes:
                # Convert class object to dict format expected by HybridStore
                class_dict = {
                    'class_name': getattr(cls, 'class_name', ''),
                    'source_code': getattr(cls, 'source_code', ''),
                    'file_path': getattr(cls, 'file_path', ''),
                    'constructor_declaration': getattr(cls, 'constructor_declaration', ''),
                    'method_declarations': getattr(cls, 'method_declarations', []),
                    'references': getattr(cls, 'references', [])
                }
                class_data.append(class_dict)

            success = self.vector_store.add_vectors("_class", class_data)
            log_message(f"[CodeIndexingService] Classes storage {'succeeded' if success else 'failed'} ({len(class_data)} items)", logger=self.logger)

        # Store special files
        if special_files:
            special_data = []
            for special in special_files:
                # Convert special file object to dict format expected by HybridStore
                special_dict = {
                    'content': getattr(special, 'content', ''),
                    'file_path': getattr(special, 'file_path', ''),
                    'file_type': getattr(special, 'file_type', ''),
                    'chunk_index': getattr(special, 'chunk_index', 0),
                    'total_chunks': getattr(special, 'total_chunks', 1),
                    'token_count': getattr(special, 'token_count', 0)
                }
                special_data.append(special_dict)

            success = self.vector_store.add_vectors("_special", special_data)
            log_message(f"[CodeIndexingService] Special files storage {'succeeded' if success else 'failed'} ({len(special_data)} items)", logger=self.logger)
    
    def _get_db_uri(self, codebase_name: str) -> str:
        """Get the URI for the vector database."""
        return get_db_uri(codebase_name, self._get_config_manager())

    def _find_references_optimized(
        self,
        file_languages: List[tuple],
        classes: List[Class],
        methods: List[Method],
        class_names: List[str],
        method_names: List[str]
    ) -> Dict[str, Dict[str, List[Dict[str, Any]]]]:
        """
        Find references using the optimized O(n log n) approach instead of O(n²).

        Args:
            file_languages: List of (file_path, language) tuples
            classes: List of parsed classes
            methods: List of parsed methods
            class_names: List of class names to find references for
            method_names: List of method names to find references for

        Returns:
            Dictionary mapping 'class' and 'method' to dictionaries of name -> references
        """
        # Create optimized reference discovery instance
        ref_discovery = OptimizedReferenceDiscovery(logger=self.logger or logging.getLogger(__name__))

        # Build symbol index from parsed classes and methods
        files_and_symbols = []
        file_symbols_map: Dict[str, List[Symbol]] = {}

        # Group symbols by file
        for method in methods:
            file_path = method.file_path
            if file_path not in file_symbols_map:
                file_symbols_map[file_path] = []

            symbol = Symbol(
                name=method.name,
                type='function',
                file_path=file_path,
                line_number=1  # We don't have line numbers from simple parser
            )
            file_symbols_map[file_path].append(symbol)

        for cls in classes:
            file_path = cls.file_path
            if file_path not in file_symbols_map:
                file_symbols_map[file_path] = []

            symbol = Symbol(
                name=cls.name,
                type='class',
                file_path=file_path,
                line_number=1  # We don't have line numbers from simple parser
            )
            file_symbols_map[file_path].append(symbol)

        # Convert to the format expected by the optimizer
        for file_path, symbols in file_symbols_map.items():
            files_and_symbols.append((file_path, symbols))

        # Build symbol index
        log_message(f"[ReferenceOptimizer] Building symbol index for {len(files_and_symbols)} files", logger=self.logger)
        ref_discovery.build_symbol_index(files_and_symbols)

        # Find references using parallel processing with timeout fallback
        log_message("[ReferenceOptimizer] Finding references using parallel processing", logger=self.logger)

        # Try the optimized approach first, but with a fallback to fast mode
        try:
            import time

            start_time = time.time()
            optimized_references = ref_discovery.find_references_parallel(max_workers=4)
            elapsed_time = time.time() - start_time

            log_message(f"[OptimizedReferences] Completed in {elapsed_time:.2f} seconds", logger=self.logger)

        except Exception as e:
            log_message(f"[OptimizedReferences] Full regex approach failed, trying fast mode: {e}", logger=self.logger, level="warning")

            try:
                optimized_references = ref_discovery.find_references_fast(max_workers=4)
                log_message("[OptimizedReferences] Fast mode completed successfully", logger=self.logger)

            except Exception as e2:
                log_message(f"[OptimizedReferences] Fast mode also failed, using ultra-simple approach: {e2}", logger=self.logger, level="warning")

                # Ultra-simple fallback
                simple_discovery = create_simple_reference_discovery(classes, methods, self.logger)
                simple_references = simple_discovery.find_references_simple(file_languages)

                # Convert to expected format
                optimized_references = {}
                for symbol_name, refs in simple_references.get('method', {}).items():
                    optimized_references[symbol_name] = refs
                for symbol_name, refs in simple_references.get('class', {}).items():
                    optimized_references[symbol_name] = refs

        # Convert to the format expected by the existing code
        references: Dict[str, Dict[str, List[Dict[str, Any]]]] = {'class': {}, 'method': {}}

        for symbol_name, refs in optimized_references.items():
            # Convert Reference objects to dictionaries
            ref_dicts: List[Dict[str, Any]] = []
            for ref in refs:
                if hasattr(ref, 'file_path'):
                    # It's a Reference object
                    ref_dicts.append({
                        'file': ref.file_path,
                        'line': ref.line_number,
                        'column': 1,  # Default column
                        'text': ref.context
                    })
                elif isinstance(ref, dict):
                    # It's already a dict
                    ref_dicts.append(ref)
                else:
                    # Convert to dict format
                    ref_dicts.append({
                        'file': str(ref),
                        'line': 1,
                        'column': 1,
                        'text': str(ref)
                    })

            # Determine if this is a class or method reference
            if symbol_name in class_names:
                references['class'][symbol_name] = ref_dicts
            if symbol_name in method_names:
                references['method'][symbol_name] = ref_dicts

        # Log statistics
        stats = ref_discovery.get_statistics()
        log_message(f"[ReferenceOptimizer] Statistics: {stats}", logger=self.logger)

        total_refs = sum(len(refs) for refs in optimized_references.values())
        log_message(f"[ReferenceOptimizer] Found {total_refs} total references for {len(optimized_references)} symbols", logger=self.logger)

        return references

    def analyze_chunking_strategy(self, file_list: List[str]) -> Dict[str, Any]:
        """
        Analyze files and recommend chunking strategies.
        This method demonstrates how smart chunking would work.

        Args:
            file_list: List of file paths to analyze

        Returns:
            Dictionary with chunking analysis and recommendations
        """
        chunker = SmartChunker(ChunkingConfig())
        analysis: Dict[str, Any] = {
            'total_files': len(file_list),
            'strategies': {'full_file': 0, 'function_based': 0, 'section_based': 0, 'line_based': 0},
            'file_details': [],
            'recommendations': []
        }

        for file_path in file_list:
            try:
                # Count lines in file
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    line_count = sum(1 for _ in f)

                # Determine strategy
                strategy = chunker.determine_strategy(file_path, line_count)
                strategies = analysis['strategies']
                if isinstance(strategies, dict):
                    strategies[strategy.value] = strategies.get(strategy.value, 0) + 1

                file_details = analysis['file_details']
                if isinstance(file_details, list):
                    file_details.append({
                        'file_path': file_path,
                        'line_count': line_count,
                        'strategy': strategy.value
                    })

            except Exception as e:
                log_message(f"Error analyzing file {file_path}: {e}", logger=self.logger, level="warning")

        # Generate recommendations
        total_files = analysis['total_files']
        strategies = analysis['strategies']
        recommendations = analysis['recommendations']

        if isinstance(strategies, dict) and isinstance(recommendations, list):
            if strategies.get('line_based', 0) > total_files * 0.1:
                recommendations.append("Consider splitting very large files (>5000 lines) for better performance")

            if strategies.get('full_file', 0) > total_files * 0.8:
                recommendations.append("Most files are small - full file embedding strategy is optimal")

            if strategies.get('function_based', 0) > total_files * 0.5:
                recommendations.append("Many medium-sized files - function-based chunking recommended")

        log_message(f"[ChunkingAnalysis] Strategy distribution: {analysis['strategies']}", logger=self.logger)

        return analysis


    
    def _update_references(
        self, 
        classes: List[Class], 
        methods: List[Method], 
        references: Dict[str, Dict[str, List[Dict[str, Any]]]]
    ) -> None:
        """Update classes and methods with reference information."""
        # Create dictionaries for quick lookup of classes and methods by name.
        class_dict = {cls.name: cls for cls in classes}
        method_dict = {(method.class_name, method.name): method for method in methods}
        
        # Update class references with resolved references from the codebase.
        for class_name, refs in references.get('class', {}).items():
            if class_name in class_dict:
                class_dict[class_name].references = refs  # type: ignore

        # Update method references with resolved references from the codebase.
        for method_name, refs in references.get('method', {}).items():
            # Find all methods with this name (across different classes) for accurate reference resolution.
            for key, method in method_dict.items():
                if key[1] == method_name:
                    method.references = refs  # type: ignore
    

