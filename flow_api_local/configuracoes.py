from dotenv import load_dotenv
import os
import logging

logger = logging.getLogger(__name__)

load_dotenv()

client_id = os.getenv("CLIENT_ID")
client_secret = os.getenv("CLIENT_SECRET")
app_to_access = os.getenv("APP_TO_ACCESS")
flow_tenant = os.getenv("FLOW_TENANT")

# Verifica se todas as variáveis necessárias estão presentes
required_vars = {
    "CLIENT_ID": client_id,
    "CLIENT_SECRET": client_secret,
    "APP_TO_ACCESS": app_to_access,
    "FLOW_TENANT": flow_tenant
}

missing_vars = [var for var, value in required_vars.items() if not value]

if missing_vars:
    logger.error(f"Variáveis de ambiente ausentes: {', '.join(missing_vars)}")
    raise ValueError(f"Variáveis de ambiente ausentes: {', '.join(missing_vars)}")