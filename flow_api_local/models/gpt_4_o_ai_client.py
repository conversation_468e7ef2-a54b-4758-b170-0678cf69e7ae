from flow_api.clients.open_ai_client import OpenAIClient


class GPT4oAIModel(OpenAIClient):

    def __init__(self):
        super().__init__()
        self.__model_name = "gpt-4o-mini"

    def _get_data(self, sys_prompt: str, user_prompt: str) -> dict:
        data = super()._get_data(sys_prompt, user_prompt)
        data["model"] = self.__model_name
        return data

    def _get_headers(self, token: str) -> dict:
        headers = super()._get_headers(token)
        headers["flowAgent"] = self.__model_name
        return headers