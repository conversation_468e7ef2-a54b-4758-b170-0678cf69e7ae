from flow_api.clients.bedrock_ai_client import BedrockAIClient


class Claude37AIModel(BedrockAIClient):
    def __init__(self):
        super().__init__()
        self.__model_name = "anthropic.claude-37-sonnet"
        self.__api_version = "bedrock-2023-05-31"

    def _get_data(self, sys_prompt: str, user_prompt: str) -> dict:
        data = super()._get_data(sys_prompt, user_prompt)
        data["anthropic_version"] = self.__api_version
        data["allowedModels"] = [self.__model_name]
        return data

    def _get_headers(self, token: str)-> dict:
        headers = super()._get_headers(token)
        headers["flowAgent"] = self.__model_name
        return headers