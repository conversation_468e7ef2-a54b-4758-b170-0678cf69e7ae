# FlowRAG Library

A powerful library for semantic code search and natural language querying of codebases.

> ### Exclusive for use with CI&T Flow APIs.

## Overview

FlowRAG is a library that enables semantic search and natural language interaction with code repositories, understanding the codebase structure and relationships between code entities.
Built with a hexagonal architecture for flexibility and adaptability, it supports the following languages:

- **Python** - Full support with Tree-sitter parsing
- **JavaScript/TypeScript** - Complete syntax analysis
- **Java** - Comprehensive code understanding
- **VB6** - Legacy code support with optimized parsing

Key features:

- **Advanced code parsing** with Tree-sitter and specialized parsers for precise code understanding
- **Hybrid search engine** combining BM25 text search with selective semantic embeddings
- **Intelligent query processing** with automatic language detection and query expansion
- **Special files support** for documentation (README.md, configs, JSON files)
- **Smart reference discovery** for understanding code relationships and dependencies
- **Multiple output formats** (JSON, Markdown, plain text, custom formats)
- **Flexible adapter architecture** for swappable components
- **Web-based chat interface** for exploring codebases
- **Simple library interface** for easy integration
- **Context-only mode** for retrieving relevant code without AI processing

## Supported Languages

The library currently supports parsing the following languages:

| Language       | File Extensions                | Features Supported                           |
| -------------- | ------------------------------ | -------------------------------------------- |
| **Python**     | `.py`, `.pyx`                  | Classes, methods, functions, docstrings      |
| **JavaScript** | `.js`, `.jsx`, `.mjs`          | Classes, functions, methods, arrow functions |
| **TypeScript** | `.ts`, `.tsx`                  | Classes, interfaces, functions, methods      |
| **Java**       | `.java`                        | Classes, methods, interfaces, constructors   |
| **VB6**        | `.frm`, `.bas`, `.cls`, `.vbp` | Forms, modules, classes, functions, subs     |

## Special Files Support

FlowRAG also indexes and searches special documentation files:

| File Type         | Extensions                                     | Content Indexed           |
| ----------------- | ---------------------------------------------- | ------------------------- |
| **Documentation** | `README.md`, `*.md`                            | Full text content         |
| **Configuration** | `*.json`, `*.yaml`, `*.yml`, `*.toml`, `*.ini` | Structured content        |
| **Project Files** | `package.json`, `requirements.txt`, `pom.xml`  | Dependencies and metadata |
| **HTML**          | `*.html`, `*.htm`                              | Full text content         |

## Architecture

The library follows a hexagonal (ports and adapters) architecture:

- **Adapters Layer**: Concrete implementations (parsers, embeddings, vector stores)
- **Application Layer**: Use cases coordinating domain functionality
- **Domain Layer**: Core business logic and entity models
- **Infrastructure Layer**: External connections and configuration
- **Storage Layer**: SQLite FTS5 database with selective embedding cache

## Quick Start (as a library)

```python
import flow_rag

# Optional: Start FlowAPI connection to be ready before queries
if flow_rag.check_connection():
    print("✅ FlowAPI connection ready")

# Initialize and index a codebase
flow_rag.initialize("/path/to/your/codebase")

# Query with AI processing
response = flow_rag.query(
    "What does this project do?",
    "/path/to/your/codebase"
)
print(response)

# Get only relevant context (no AI processing)
context = flow_rag.get_context(
    "authentication functions",
    "/path/to/your/codebase"
)
print(context)
```

## Installation and Usage

All installation and usage instructions, including command-line and programmatic examples, have been moved to:

- **[USAGE.md](USAGE.md)** - Complete usage guide for Unix/Linux/macOS
- **[USAGE_WINDOWS.md](USAGE_WINDOWS.md)** - Windows-specific installation and setup guide

Please refer to the appropriate file for your operating system.

## Core Components

### **Search Engine**

- **BM25 Text Search**: Fast lexical search using SQLite FTS5 with method name boosting
- **Semantic Embeddings**: Selective embedding generation for enhanced results
- **Intelligent Chunking**: Handles large content by splitting into semantic chunks and combining embeddings
- **Intelligent Reranking**: Combines text and semantic scores for optimal ranking

### **Code Analysis**

- **Tree-sitter Parsing**: Precise syntax analysis for supported languages
- **Optimized VB6 Parser**: High-performance regex-based parser for legacy code
- **Reference Discovery**: Intelligent detection of code relationships and dependencies
- **Large Content Handling**: Automatic chunking and embedding combination for files exceeding token limits

### **Storage System**

- **Unified Database**: SQLite FTS5 for all content types (methods, classes, files)
- **Embedding Cache**: Selective caching of embeddings for frequently accessed content
- **Chunked Embedding Cache**: Intelligent caching of combined embeddings for large content
- **Smart Indexing**: Direct content storage without unnecessary preprocessing

## Directory Structure

```bash
├── flow_api/                    # Code for Flow API (not part of the library)
├── flow_rag/                    # Code for FlowRAG
│   ├── adapters/                # Implementations of interfaces
│   │   ├── code_parsers/        # Tree-sitter and specialized parsers
│   │   ├── embedding_providers/ # Jina, OpenAI, and other embedding services
│   │   ├── file_loader/         # File loading and language detection
│   │   ├── llm_providers/       # Flow API client for LLM capabilities
│   │   └── vector_stores/       # HybridStore (SQLite FTS5 + embeddings)
│   ├── application/             # Use cases and business logic
│   ├── domain/                  # Core models and interfaces
│   ├── infrastructure/          # Configuration and external connections
│   │   ├── web/                 # Web interface and entrypoints
│   │   └── db/                  # Cache database implementations
│   └── utils/                   # Utility functions and helpers
├── tests/                       # Unit tests for FlowRAG
├── rag_logs/                    # Default directory for logs
└── rag_data/                    # Default directory for indexed data
```

> \*Folder flow_api is not part of the library, but it is required as LLM provider. Will be replaced by external API calls in the future.

## Troubleshooting

If you encounter issues:

1. Check the logs directory for detailed error information:

   ```bash
   ls -lt rag_logs/
   # Or your custom LOGS_BASE_PATH in .env
   ```

2. Check the indexed data directory:

   ```bash
   ls -lt rag_data/
   # Or your custom STORE_BASE_PATH in .env
   # Database files are stored as {codebase_name}.db
   ```

3. Make sure you have the correct API keys set.

## Extending the Library

The hexagonal architecture makes it easy to add new adapters:

1. **New Language Support**: Implement a parser extending `BaseCodeParser` and register it in `auto_register.py`
2. **New Embedding Provider**: Implement `EmbeddingsPort` interface and add to the factory
3. **New Vector Store**: Implement `VectorStorePort` interface for alternative storage backends
4. **New LLM Provider**: Implement `LLMPort` interface for different AI services

Example - Adding a new language parser:

```python
# In flow_rag/adapters/code_parsers/auto_register.py
ParserRegistry.register(LanguageEnum.RUST, RustCodeParser)
```

For more detailed usage, see the [USAGE.md](USAGE.md) file.

## References

- [Long context prompting tips](https://docs.anthropic.com/en/docs/build-with-claude/prompt-engineering/long-context-tips#example-multi-document-structure)
- [Using Code Syntax Parsing for Generative AI](https://windsurf.com/blog/using-code-syntax-parsing-for-generative-ai)
- [How Cody understands your codebase](https://blog.lancedb.com/rag-codebase-1/)
- [How Cursor indexes codebases fast](https://read.engineerscodex.com/p/how-cursor-indexes-codebases-fast)
- [Conversational semantic code search](https://news.ycombinator.com/item?id=35000562)
- [Precise Zero-Shot Dense Retrieval without Relevance Labels](https://arxiv.org/abs/2212.10496)
- [Introducing Rerank 3](https://cohere.com/blog/rerank-3)
- [Hybrid Search](https://lancedb.github.io/lancedb/hybrid_search/eval/)
- [Hybrid Search: Combining BM25 and Semantic Search](https://lancedb.substack.com/p/hybrid-search-combining-bm25-and)
- [Tree-sitter](https://tree-sitter.github.io/tree-sitter/)
- [An attempt to build cursor's @codebase feature (Main inspiration)](https://blog.lancedb.com/rag-codebase-1/)
- [CI&T Flow API](https://flow.ciandt.com/docs/developerPortal/llm/ai-completions-api)
