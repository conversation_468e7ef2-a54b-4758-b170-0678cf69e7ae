# FlowAPI Connection Check Guide

## Overview

The `check_connection()` function allows you to verify FlowAPI connectivity and refresh authentication tokens before making queries. This is particularly useful for web servers and applications that want to avoid delays on the first request.

## Why Use check_connection()?

### The Problem
- The first FlowAPI request can take several seconds while:
  - Checking authentication tokens
  - Refreshing expired tokens
  - Establishing initial connection

### The Solution
- Call `check_connection()` proactively to:
  - Verify connectivity upfront
  - Refresh tokens in advance
  - Ensure subsequent requests are fast

## Usage

### Basic Usage

```python
import flow_rag

# Check connection before processing
if flow_rag.check_connection():
    print("✅ Ready to process queries")
    # Your application logic here
else:
    print("❌ Connection failed - check network/credentials")
```

### Web Server Integration

```python
from flask import Flask
import flow_rag

app = Flask(__name__)

@app.before_first_request
def initialize_flowapi():
    """Initialize FlowAPI connection before handling requests."""
    print("🔗 Checking FlowAPI connection...")
    if flow_rag.check_connection():
        print("✅ FlowAPI ready")
    else:
        print("❌ FlowAPI connection failed")

@app.route('/query')
def handle_query():
    # Now queries will be fast since connection is pre-established
    result = flow_rag.query(request.args.get('q'), '/path/to/codebase')
    return result
```

### FastAPI Integration

```python
from fastapi import FastAPI
import flow_rag

app = FastAPI()

@app.on_event("startup")
async def startup_event():
    """Check FlowAPI connection on startup."""
    print("🔗 Initializing FlowAPI connection...")
    if flow_rag.check_connection():
        print("✅ FlowAPI connection established")
    else:
        print("❌ FlowAPI connection failed")

@app.get("/query")
async def query_endpoint(q: str):
    # Fast queries since connection is pre-established
    return flow_rag.query(q, '/path/to/codebase')
```

### Batch Processing

```python
import flow_rag

def process_multiple_queries(queries, codebase_path):
    """Process multiple queries efficiently."""
    
    # Check connection once before processing all queries
    if not flow_rag.check_connection():
        print("❌ FlowAPI connection failed")
        return []
    
    # Initialize codebase once
    flow_rag.initialize(codebase_path)
    
    results = []
    for query in queries:
        # These will be fast since connection is pre-established
        result = flow_rag.query(query, codebase_path)
        results.append(result)
    
    return results
```

## What It Checks

The `check_connection()` function verifies:

1. **LLM Provider Connection**
   - Tests connection to FlowAPI LLM services
   - Verifies authentication tokens
   - Refreshes tokens if needed

2. **Embedding Provider Connection**
   - Tests connection to FlowAPI embedding services
   - Verifies embedding model availability
   - Checks authentication for embedding endpoints

## Return Values

- `True`: All connections successful, ready for queries
- `False`: One or more connections failed

## Error Handling

```python
import flow_rag

def safe_query_processing():
    try:
        # Check connection with error handling
        if flow_rag.check_connection():
            # Proceed with queries
            result = flow_rag.query("How does auth work?", "/path/to/code")
            return result
        else:
            return "Service temporarily unavailable"
    
    except Exception as e:
        print(f"Error: {e}")
        return "Connection error occurred"
```

## Performance Benefits

### Without check_connection()
```
First query:  ~5-10 seconds (includes token refresh)
Second query: ~1-2 seconds
Third query:  ~1-2 seconds
```

### With check_connection()
```
check_connection(): ~3-5 seconds (one-time cost)
First query:        ~1-2 seconds
Second query:       ~1-2 seconds
Third query:        ~1-2 seconds
```

## Best Practices

### 1. Web Servers
```python
# Call during application startup
@app.before_first_request
def setup():
    flow_rag.check_connection()
```

### 2. Long-Running Applications
```python
# Check periodically to refresh tokens
import schedule
import time

def refresh_connection():
    flow_rag.check_connection()

# Refresh every hour
schedule.every().hour.do(refresh_connection)

while True:
    schedule.run_pending()
    time.sleep(60)
```

### 3. CLI Applications
```python
# Check once at startup
def main():
    print("Initializing...")
    if not flow_rag.check_connection():
        print("❌ Cannot connect to FlowAPI")
        return 1
    
    # Process commands...
```

## Troubleshooting

### Connection Fails
1. **Check Internet Connectivity**
   ```bash
   ping flow.ciandt.com
   ```

2. **Verify Credentials**
   - Ensure FlowAPI credentials are properly configured
   - Check if tokens have expired

3. **Service Status**
   - Verify FlowAPI services are operational
   - Check for maintenance windows

### Partial Failures
- If only LLM or embedding connection fails:
  - Check specific service endpoints
  - Verify model availability
  - Review authentication for specific services

## Integration Examples

See the `examples/check_connection_example.py` file for a complete working example.

## Related Functions

- `flow_rag.initialize()` - Index a codebase
- `flow_rag.query()` - Query with AI processing
- `flow_rag.get_context()` - Get context without AI processing

## Technical Details

The function internally:
1. Creates LLM and embedding provider instances
2. Calls their respective `check_connection()` methods
3. Returns `True` only if both succeed
4. Handles fallbacks for providers without connection checking
