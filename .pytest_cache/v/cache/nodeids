["tests/test_application_services.py::TestApplicationServices::test_code_indexing_service", "tests/test_application_services.py::TestApplicationServices::test_code_indexing_with_progress", "tests/test_application_services.py::TestApplicationServices::test_query_processing_performance", "tests/test_application_services.py::TestApplicationServices::test_query_processing_service", "tests/test_application_services.py::TestApplicationServices::test_query_processing_with_hyde_disabled", "tests/test_code_parser.py::test_get_language_from_extension", "tests/test_code_parser.py::test_load_files", "tests/test_code_parser.py::test_should_ignore_path", "tests/test_embedding_providers.py::TestJinaEmbeddingProvider::test_batching_and_empty", "tests/test_embedding_providers.py::TestJinaEmbeddingProvider::test_invalid_key", "tests/test_embedding_providers.py::TestJinaEmbeddingProvider::test_properties", "tests/test_embedding_providers.py::TestOpenAIEmbeddingProvider::test_batching_and_empty", "tests/test_embedding_providers.py::TestOpenAIEmbeddingProvider::test_invalid_key", "tests/test_embedding_providers.py::TestOpenAIEmbeddingProvider::test_properties", "tests/test_flow_rag_client.py::TestFlowRAGClient::test_get_context", "tests/test_flow_rag_client.py::TestFlowRAGClient::test_get_context_invalid_path", "tests/test_flow_rag_client.py::TestFlowRAGClient::test_initialize", "tests/test_flow_rag_client.py::TestFlowRAGClient::test_initialize_invalid_path", "tests/test_flow_rag_client.py::TestFlowRAGClient::test_initialize_with_progress_callback", "tests/test_flow_rag_client.py::TestFlowRAGClient::test_query_basic", "tests/test_flow_rag_client.py::TestFlowRAGClient::test_query_invalid_path", "tests/test_flow_rag_client.py::TestFlowRAGClient::test_query_with_output_format", "tests/test_flow_rag_client.py::TestFlowRAGClient::test_query_with_return_context", "tests/test_hybrid_store.py::TestHybridStore::test_add_vectors_classes", "tests/test_hybrid_store.py::TestHybridStore::test_add_vectors_methods", "tests/test_hybrid_store.py::TestHybridStore::test_add_vectors_special_files", "tests/test_hybrid_store.py::TestHybridStore::test_cleanup_cache", "tests/test_hybrid_store.py::TestHybridStore::test_close", "tests/test_hybrid_store.py::TestHybridStore::test_connect", "tests/test_hybrid_store.py::TestHybridStore::test_create_table", "tests/test_hybrid_store.py::TestHybridStore::test_initialization", "tests/test_hybrid_store.py::TestHybridStore::test_optimize_indexes", "tests/test_hybrid_store.py::TestHybridStore::test_search_hybrid_methods", "tests/test_hybrid_store.py::TestHybridStore::test_search_hybrid_no_results", "tests/test_hybrid_store.py::TestHybridStore::test_search_hybrid_with_semantic_reranking", "tests/test_optimized_vb6_parser.py::TestOptimizedVB6CodeParser::test_find_references", "tests/test_optimized_vb6_parser.py::TestOptimizedVB6CodeParser::test_get_supported_languages", "tests/test_optimized_vb6_parser.py::TestOptimizedVB6CodeParser::test_parse_empty_file", "tests/test_optimized_vb6_parser.py::TestOptimizedVB6CodeParser::test_parse_multiple_files", "tests/test_optimized_vb6_parser.py::TestOptimizedVB6CodeParser::test_parse_simple_vb6_form", "tests/test_optimized_vb6_parser.py::TestOptimizedVB6CodeParser::test_parse_vb6_class", "tests/test_optimized_vb6_parser.py::TestOptimizedVB6CodeParser::test_parse_vb6_module", "tests/test_optimized_vb6_parser.py::TestOptimizedVB6CodeParser::test_performance_with_large_content", "tests/test_utils.py::TestReferenceOptimizer::test_find_references_parallel", "tests/test_utils.py::TestReferenceOptimizer::test_optimized_reference_discovery_creation", "tests/test_utils.py::TestReferenceOptimizer::test_symbol_creation", "tests/test_utils.py::TestSimpleReferenceDiscovery::test_create_simple_reference_discovery", "tests/test_utils.py::TestSimpleReferenceDiscovery::test_find_references_empty_files", "tests/test_utils.py::TestSimpleReferenceDiscovery::test_find_references_no_symbols", "tests/test_utils.py::TestSimpleReferenceDiscovery::test_find_references_simple", "tests/test_utils.py::TestSimpleReferenceDiscovery::test_simple_reference_discovery_creation", "tests/test_utils.py::TestSmartChunking::test_chunk_code_file", "tests/test_utils.py::TestSmartChunking::test_chunk_file_short", "tests/test_utils.py::TestSmartChunking::test_chunk_file_simple", "tests/test_utils.py::TestSmartChunking::test_chunking_config", "tests/test_utils.py::TestSmartChunking::test_smart_chunker_creation"]